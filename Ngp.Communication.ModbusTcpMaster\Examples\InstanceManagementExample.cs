using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Fluent;
using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Examples;

/// <summary>
/// Example demonstrating instance management and multiple connections to same endpoint
/// </summary>
public class InstanceManagementExample
{
    /// <summary>
    /// Run the instance management example
    /// </summary>
    public static async Task RunAsync()
    {
        // Create logger
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<ModbusMaster>();

        Console.WriteLine("=== Instance Management Example ===\n");

        // Example 1: Multiple instances connecting to same endpoint
        await MultipleInstancesExample(logger);

        Console.WriteLine("\n" + new string('=', 50) + "\n");

        // Example 2: Single instance with multiple connections
        await SingleInstanceMultipleConnectionsExample(logger);

        Console.WriteLine("\n" + new string('=', 50) + "\n");

        // Example 3: Dynamic write insertion across instances
        await DynamicWriteAcrossInstancesExample(logger);
    }

    /// <summary>
    /// Example 1: Multiple instances connecting to same endpoint
    /// Each instance has its own unique identifier and cannot be merged
    /// </summary>
    private static async Task MultipleInstancesExample(ILogger<ModbusMaster> logger)
    {
        Console.WriteLine("Example 1: Multiple Instances to Same Endpoint");
        Console.WriteLine("Each instance maintains separate connections and cannot be merged\n");

        // Create first instance for production monitoring
        var productionInstance = ModbusMasterInstance.Create("ProductionMonitor");
        using var productionMaster = new ModbusMasterBuilder(logger, productionInstance)
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .AddDevice("*************", 502)
                .WithTimeout(TimeSpan.FromSeconds(5))
                .WithGapTime(TimeSpan.FromMilliseconds(100))
                .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 50,
                    pollingInterval: TimeSpan.FromSeconds(2))
            .EndDevice()
            .Build();

        // Create second instance for quality control
        var qualityInstance = ModbusMasterInstance.Create("QualityControl");
        using var qualityMaster = new ModbusMasterBuilder(logger, qualityInstance)
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .AddDevice("*************", 502) // Same endpoint but different instance
                .WithTimeout(TimeSpan.FromSeconds(3))
                .WithGapTime(TimeSpan.FromMilliseconds(50))
                .PollHoldingRegisters(unitId: 2, startAddress: 100, count: 30,
                    pollingInterval: TimeSpan.FromSeconds(1))
            .EndDevice()
            .Build();

        // Create third instance for maintenance
        var maintenanceInstance = ModbusMasterInstance.Create("Maintenance");
        using var maintenanceMaster = new ModbusMasterBuilder(logger, maintenanceInstance)
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .AddDevice("*************", 502) // Same endpoint but different instance
                .WithTimeout(TimeSpan.FromSeconds(10))
                .WithGapTime(TimeSpan.FromMilliseconds(200))
                .PollInputRegisters(unitId: 3, startAddress: 200, count: 20,
                    pollingInterval: TimeSpan.FromSeconds(5))
            .EndDevice()
            .Build();

        Console.WriteLine($"Production Instance: {productionMaster.Instance}");
        Console.WriteLine($"Quality Instance: {qualityMaster.Instance}");
        Console.WriteLine($"Maintenance Instance: {maintenanceMaster.Instance}");

        // Start all instances
        await productionMaster.StartAsync();
        await qualityMaster.StartAsync();
        await maintenanceMaster.StartAsync();

        Console.WriteLine("\nAll instances started. Each has independent connections to *************:502");
        Console.WriteLine("Press any key to continue...");
        Console.ReadKey();

        // Stop all instances
        await productionMaster.StopAsync();
        await qualityMaster.StopAsync();
        await maintenanceMaster.StopAsync();
    }

    /// <summary>
    /// Example 2: Single instance with multiple connections to same endpoint
    /// </summary>
    private static async Task SingleInstanceMultipleConnectionsExample(ILogger<ModbusMaster> logger)
    {
        Console.WriteLine("Example 2: Single Instance with Multiple Connections");
        Console.WriteLine("One instance can have multiple connections to the same endpoint for higher throughput\n");

        // Create instance with multiple connections to same endpoint
        var highThroughputInstance = ModbusMasterInstance.Create("HighThroughput");
        using var modbusMaster = new ModbusMasterBuilder(logger, highThroughputInstance)
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .WithParallelProcessing(true)
            .WithMaxParallelRequests(100)
            .AddDevice("*************", 502)
                .WithMultipleConnections(5) // Create 5 connections to same endpoint
                .WithTimeout(TimeSpan.FromSeconds(3))
                .WithGapTime(TimeSpan.FromMilliseconds(10))
                .WithParallelProcessing(true)
                .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 100,
                    pollingInterval: TimeSpan.FromSeconds(1))
                .PollInputRegisters(unitId: 1, startAddress: 1000, count: 200,
                    pollingInterval: TimeSpan.FromMilliseconds(500))
                .PollCoils(unitId: 1, startAddress: 0, count: 64,
                    pollingInterval: TimeSpan.FromMilliseconds(200))
            .EndDevice()
            .Build();

        Console.WriteLine($"Instance: {modbusMaster.Instance}");

        // Get connection contexts for the endpoint
        var connections = modbusMaster.GetConnectionContexts("*************", 502);
        Console.WriteLine($"Number of connections to *************:502: {connections.Count}");
        foreach (var conn in connections)
        {
            Console.WriteLine($"  Connection: {conn}");
        }

        await modbusMaster.StartAsync();

        Console.WriteLine("\nInstance started with multiple connections for high throughput");
        Console.WriteLine("Press any key to continue...");
        Console.ReadKey();

        await modbusMaster.StopAsync();
    }

    /// <summary>
    /// Example 3: Dynamic write insertion across different instances
    /// </summary>
    private static async Task DynamicWriteAcrossInstancesExample(ILogger<ModbusMaster> logger)
    {
        Console.WriteLine("Example 3: Dynamic Write Insertion Across Instances");
        Console.WriteLine("Each instance can insert write commands independently\n");

        // Create control instance
        var controlInstance = ModbusMasterInstance.Create("ControlSystem");
        using var controlMaster = new ModbusMasterBuilder(logger, controlInstance)
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .AddDevice("*************", 502)
                .WithDynamicWriteInsertion(true)
                .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 50,
                    pollingInterval: TimeSpan.FromSeconds(1))
            .EndDevice()
            .Build();

        // Create monitoring instance
        var monitorInstance = ModbusMasterInstance.Create("MonitoringSystem");
        using var monitorMaster = new ModbusMasterBuilder(logger, monitorInstance)
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .AddDevice("*************", 502)
                .WithDynamicWriteInsertion(true)
                .PollInputRegisters(unitId: 1, startAddress: 100, count: 30,
                    pollingInterval: TimeSpan.FromSeconds(2))
            .EndDevice()
            .Build();

        Console.WriteLine($"Control Instance: {controlMaster.Instance}");
        Console.WriteLine($"Monitor Instance: {monitorMaster.Instance}");

        await controlMaster.StartAsync();
        await monitorMaster.StartAsync();

        Console.WriteLine("\nBoth instances started. Inserting write commands...");

        // Insert write commands from control instance
        await controlMaster.QueueWriteRequestAsync("*************", 502, 1, 10, 1000);
        Console.WriteLine("Control instance: Queued write to register 10 = 1000");

        await Task.Delay(1000);

        // Insert write commands from monitoring instance
        await monitorMaster.QueueWriteRequestAsync("*************", 502, 1, 20, 2000);
        Console.WriteLine("Monitor instance: Queued write to register 20 = 2000");

        await Task.Delay(1000);

        // Insert multiple writes from control instance
        await controlMaster.QueueMultipleWriteRequestAsync("*************", 502, 1, 30, 
            new ushort[] { 3000, 3001, 3002 });
        Console.WriteLine("Control instance: Queued multiple writes to registers 30-32");

        Console.WriteLine("\nWrite commands inserted. Each instance maintains independent queues.");
        Console.WriteLine("Press any key to stop...");
        Console.ReadKey();

        await controlMaster.StopAsync();
        await monitorMaster.StopAsync();
    }
}
