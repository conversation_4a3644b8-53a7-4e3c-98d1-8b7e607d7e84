namespace Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Unique identifier for a connection
/// </summary>
public class ConnectionIdentifier
{
    /// <summary>
    /// IP address
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;
    
    /// <summary>
    /// Port number
    /// </summary>
    public int Port { get; set; }
    
    /// <summary>
    /// Connection instance ID (for multiple connections to same endpoint)
    /// </summary>
    public string InstanceId { get; set; } = string.Empty;
    
    /// <summary>
    /// Initialize connection identifier
    /// </summary>
    public ConnectionIdentifier()
    {
    }
    
    /// <summary>
    /// Initialize connection identifier
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="instanceId">Instance ID</param>
    public ConnectionIdentifier(string ipAddress, int port, string? instanceId = null)
    {
        IpAddress = ipAddress;
        Port = port;
        InstanceId = instanceId ?? Guid.NewGuid().ToString("N")[..8];
    }
    
    /// <summary>
    /// Get the endpoint key (IP:Port)
    /// </summary>
    /// <returns>Endpoint key</returns>
    public string GetEndpointKey()
    {
        return $"{IpAddress}:{Port}";
    }
    
    /// <summary>
    /// Get the full connection key (IP:Port:InstanceId)
    /// </summary>
    /// <returns>Full connection key</returns>
    public string GetConnectionKey()
    {
        return $"{IpAddress}:{Port}:{InstanceId}";
    }
    
    /// <summary>
    /// Create a default connection identifier for an endpoint
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>Default connection identifier</returns>
    public static ConnectionIdentifier CreateDefault(string ipAddress, int port)
    {
        return new ConnectionIdentifier(ipAddress, port, "default");
    }
    
    /// <summary>
    /// Create a new connection identifier for an endpoint
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>New connection identifier</returns>
    public static ConnectionIdentifier CreateNew(string ipAddress, int port)
    {
        return new ConnectionIdentifier(ipAddress, port);
    }
    
    /// <summary>
    /// Parse connection key back to identifier
    /// </summary>
    /// <param name="connectionKey">Connection key</param>
    /// <returns>Connection identifier</returns>
    public static ConnectionIdentifier Parse(string connectionKey)
    {
        var parts = connectionKey.Split(':');
        if (parts.Length < 2)
            throw new ArgumentException("Invalid connection key format", nameof(connectionKey));
        
        var ipAddress = parts[0];
        if (!int.TryParse(parts[1], out var port))
            throw new ArgumentException("Invalid port in connection key", nameof(connectionKey));
        
        var instanceId = parts.Length > 2 ? parts[2] : "default";
        
        return new ConnectionIdentifier(ipAddress, port, instanceId);
    }
    
    /// <summary>
    /// Check if this identifier equals another
    /// </summary>
    /// <param name="obj">Object to compare</param>
    /// <returns>True if equal</returns>
    public override bool Equals(object? obj)
    {
        if (obj is not ConnectionIdentifier other)
            return false;
        
        return IpAddress == other.IpAddress && 
               Port == other.Port && 
               InstanceId == other.InstanceId;
    }
    
    /// <summary>
    /// Get hash code
    /// </summary>
    /// <returns>Hash code</returns>
    public override int GetHashCode()
    {
        return HashCode.Combine(IpAddress, Port, InstanceId);
    }
    
    /// <summary>
    /// String representation
    /// </summary>
    /// <returns>String representation</returns>
    public override string ToString()
    {
        return GetConnectionKey();
    }
    
    /// <summary>
    /// Clone this identifier
    /// </summary>
    /// <returns>Cloned identifier</returns>
    public ConnectionIdentifier Clone()
    {
        return new ConnectionIdentifier(IpAddress, Port, InstanceId);
    }
    
    /// <summary>
    /// Validate the identifier
    /// </summary>
    public void Validate()
    {
        if (string.IsNullOrWhiteSpace(IpAddress))
            throw new ArgumentException("IP address cannot be null or empty", nameof(IpAddress));
        
        if (Port <= 0 || Port > 65535)
            throw new ArgumentOutOfRangeException(nameof(Port), "Port must be between 1 and 65535");
        
        if (string.IsNullOrWhiteSpace(InstanceId))
            throw new ArgumentException("Instance ID cannot be null or empty", nameof(InstanceId));
    }
}
