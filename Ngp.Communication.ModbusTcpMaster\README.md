# Ngp.Communication.ModbusTcpMaster

🚀 **企業級 Modbus TCP Master 庫** - 使用 .NET 9 開發，支援高性能、高並發的 Modbus 通訊

[![.NET 9](https://img.shields.io/badge/.NET-9.0-blue.svg)](https://dotnet.microsoft.com/download/dotnet/9.0)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](BUILD)

## ✨ 主要特性

### 🚀 **高性能架構**
- **大規模連接**: 支援同時連接 1000+ 台 Slave 裝置
- **智慧連線管理**: 一個 IP:Port 組合使用單一連線，避免資源浪費
- **平行化處理**: 支援並發請求和輪詢，最大化吞吐量
- **連線池管理**: 高效能的 TCP 連線池和資源回收

### 🔧 **完整 Modbus 支援**
- **雙協定支援**: Modbus TCP 和 Modbus RTU over TCP
- **RTU over TCP 限制**: RTU over TCP 模式強制單一問答，確保協定相容性
- **全功能碼實作**: 支援所有標準 Modbus 功能碼
- **讀寫操作**: Single/Multiple Read/Write 操作
- **錯誤處理**: 自動處理和解析 Modbus 例外碼

### 🛡️ **企業級穩定性**
- **Thread-Safe**: 所有操作都是執行緒安全的
- **自動重連**: 智慧重連機制和連線狀態管理
- **資源管理**: 完善的資源釋放和 Graceful Exit
- **錯誤恢復**: 強健的錯誤處理和恢復機制

### 📊 **智慧輪詢引擎**
- **自動分割**: 智慧分割過長的暫存器請求範圍
- **變更偵測**: 只在數值改變時觸發事件，減少不必要的處理
- **靈活配置**: 可設定輪詢間隔、範圍限制和並發數
- **效能最佳化**: 平行化輪詢處理和智慧排程

### 🔄 **進階數據轉換**
- **多種數據類型**: Int16/32/64, UInt16/32/64, Float, Double, Boolean, String
- **Endian 支援**: BigEndian, LittleEndian, BigEndianWordSwap, LittleEndianWordSwap
- **數學運算**: 支援縮放因子和偏移量
- **位元操作**: 從暫存器中提取特定位元
- **字串處理**: 多種字串編碼格式支援

### 📈 **企業級監控**
- **結構化日誌**: 使用 Microsoft.Extensions.Logging
- **即時監控**: 系統狀態、性能指標、錯誤統計
- **健康檢查**: 智慧健康狀態評估
- **詳細統計**: 端點、Slave、輪詢範圍的完整資訊

### 🎯 **開發者友善**
- **FluentAPI**: 直觀的鏈式 API 設計
- **端點特定配置**: 可針對每個 IP:Port 設定不同的 Timeout 和 Gap 時間
- **動態Write插入**: 可在輪詢過程中動態插入高優先級Write指令
- **多連線支援**: 支援同一端點的多個連線，提供更高的並發能力
- **豐富事件**: 完整的事件系統
- **詳細文檔**: 完整的 API 文檔和範例
- **單元測試**: 完整的測試覆蓋率

## 🚀 快速開始

### 安裝

```bash
# 透過 NuGet 安裝 (假設已發布)
dotnet add package Ngp.Communication.ModbusTcpMaster

# 或直接引用專案
<ProjectReference Include="path/to/Ngp.Communication.ModbusTcpMaster.csproj" />
```

### 基本使用

```csharp
using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Fluent;
using Microsoft.Extensions.Logging;

// 建立 Logger (可選)
using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
var logger = loggerFactory.CreateLogger<ModbusMaster>();

// 使用 FluentAPI 建立 ModbusMaster
using var modbusMaster = new ModbusMasterBuilder(logger)
    .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
    .WithDefaultTimeout(TimeSpan.FromSeconds(5))
    .WithParallelProcessing(true)
    .WithMaxParallelRequests(100)
    .OnDataValueUpdated((sender, e) =>
    {
        Console.WriteLine($"數據更新: {e.IpAddress}:{e.Port} Unit:{e.UnitId}");
    })
    .OnConnectionStatusChanged((sender, e) =>
    {
        Console.WriteLine($"連線狀態: {e.IpAddress}:{e.Port} -> {e.CurrentStatus}");
    })
    .Build();

// 啟動服務
await modbusMaster.StartAsync();

// 讀取 Holding Registers
var values = await modbusMaster.ReadHoldingRegistersAsync(
    "*************", 502, unitId: 1, startAddress: 0, count: 10);

Console.WriteLine($"讀取到 {values.Length} 個暫存器值");

// 寫入單一暫存器
await modbusMaster.WriteSingleRegisterAsync(
    "*************", 502, unitId: 1, address: 0, value: 1234);

// 停止服務
await modbusMaster.StopAsync();
```

## 📊 自動輪詢功能

```csharp
using var modbusMaster = new ModbusMasterBuilder(logger)
    .WithPollingSettings(enableChangeDetection: true)
    .AddDevice("*************", 502)
        // 輪詢 Holding Registers
        .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 20,
            pollingInterval: TimeSpan.FromSeconds(1))
        // 輪詢 Input Registers
        .PollInputRegisters(unitId: 1, startAddress: 100, count: 10,
            pollingInterval: TimeSpan.FromSeconds(2))
        // 輪詢 Coils
        .PollCoils(unitId: 1, startAddress: 0, count: 16,
            pollingInterval: TimeSpan.FromMilliseconds(500))
    .EndDevice()
    .AddDevice("*************", 502)
        .PollHoldingRegisters(unitId: 2, startAddress: 0, count: 50,
            pollingInterval: TimeSpan.FromSeconds(1))
    .EndDevice()
    .OnDataValueUpdated((sender, e) =>
    {
        Console.WriteLine($"輪詢數據更新: {e.IpAddress}:{e.Port} " +
                         $"Unit:{e.UnitId} {e.RegisterType} 地址:{e.StartAddress}");
    })
    .Build();

await modbusMaster.StartAsync();

// 輪詢會自動開始，持續運行直到停止
Console.WriteLine("輪詢已開始，按任意鍵停止...");
Console.ReadKey();

await modbusMaster.StopAsync();
```

## 🔄 數據轉換功能

```csharp
using var modbusMaster = new ModbusMasterBuilder(logger)
    .WithPollingSettings(enableChangeDetection: true)
    .AddDevice("*************", 502)
        .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 20,
            pollingInterval: TimeSpan.FromSeconds(1))
            // 溫度感測器 (16位元整數，縮放0.1，偏移-40)
            .WithDataConversion(0, ModbusDataType.Int16, EndianType.BigEndian,
                scaleFactor: 0.1, offset: -40.0, name: "Temperature")
            // 壓力感測器 (32位元浮點數)
            .WithDataConversion(1, ModbusDataType.Float, EndianType.BigEndian,
                name: "Pressure")
            // 計數器 (32位元無符號整數)
            .WithDataConversion(3, ModbusDataType.UInt32, EndianType.LittleEndianWordSwap,
                name: "Counter")
            // 警報位元
            .WithBooleanConversion(5, bitPosition: 0, name: "Alarm1")
            .WithBooleanConversion(5, bitPosition: 1, name: "Alarm2")
            // 裝置名稱字串
            .WithStringConversion(10, length: 8, encodingName: "ASCII", name: "DeviceName")
    .EndDevice()
    .OnDataValueUpdated((sender, e) =>
    {
        // 處理轉換後的數據
        foreach (var converted in e.ConvertedValues)
        {
            Console.WriteLine($"{converted.Config.Name}: {converted.Value} " +
                            $"({converted.Config.DataType})");
        }
    })
    .Build();

await modbusMaster.StartAsync();
```

## ⚙️ 端點特定配置

```csharp
using var modbusMaster = new ModbusMasterBuilder(logger)
    .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
    .AddDevice("*************", 502)
        // 為此端點設定特定的 Timeout 和 Gap 時間
        .WithTimeout(TimeSpan.FromSeconds(10))
        .WithGapTime(TimeSpan.FromMilliseconds(50))
        // 設定連線參數
        .WithConnectionSettings(
            connectionTimeout: TimeSpan.FromSeconds(8),
            receiveTimeout: TimeSpan.FromSeconds(8),
            maxConcurrentRequestsPerConnection: 5)
        // 設定平行處理
        .WithParallelProcessing(false) // 此端點不使用平行處理
        .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 20,
            pollingInterval: TimeSpan.FromSeconds(2))
    .EndDevice()
    .AddDevice("*************", 502)
        // 另一個端點使用不同的設定
        .WithTimeout(TimeSpan.FromSeconds(3))
        .WithGapTime(TimeSpan.FromMilliseconds(10))
        .WithParallelProcessing(true, maxParallelRequests: 20)
        .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 50,
            pollingInterval: TimeSpan.FromSeconds(1))
    .EndDevice()
    .Build();

await modbusMaster.StartAsync();

// 也可以動態設定端點配置
var endpointConfig = new EndpointConfiguration
{
    Timeout = TimeSpan.FromSeconds(15),
    GapTime = TimeSpan.FromMilliseconds(100),
    EnableParallelProcessing = false
};
modbusMaster.SetEndpointConfiguration("*************", 502, endpointConfig);
```

## 🔧 RTU over TCP 模式

```csharp
// RTU over TCP 模式會自動強制單一問答
using var modbusMaster = new ModbusMasterBuilder(logger)
    .WithProtocolMode(ModbusProtocolMode.ModbusRtuOverTcp) // 強制單一問答
    .WithDefaultTimeout(TimeSpan.FromSeconds(5))
    .AddDevice("*************", 502)
        .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 10,
            pollingInterval: TimeSpan.FromSeconds(1))
    .EndDevice()
    .Build();

await modbusMaster.StartAsync();
```

## 🚀 動態Write指令插入

```csharp
using var modbusMaster = new ModbusMasterBuilder(logger)
    .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
    .AddDevice("*************", 502)
        .WithDynamicWriteInsertion(true) // 啟用動態Write插入
        .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 100,
            pollingInterval: TimeSpan.FromSeconds(1))
    .EndDevice()
    .Build();

await modbusMaster.StartAsync();

// 在輪詢過程中動態插入高優先級Write指令
// 這些指令會在當前輪詢完成後立即執行，然後繼續輪詢

// 插入單一暫存器寫入
await modbusMaster.QueueWriteRequestAsync(
    "*************", 502, unitId: 1, address: 50, value: 1234);

// 插入單一線圈寫入
await modbusMaster.QueueWriteRequestAsync(
    "*************", 502, unitId: 1, address: 100, value: 1, isCoil: true);

// 插入多個暫存器寫入
await modbusMaster.QueueMultipleWriteRequestAsync(
    "*************", 502, unitId: 1, startAddress: 200,
    values: new ushort[] { 1000, 2000, 3000 });

// 插入多個線圈寫入
await modbusMaster.QueueMultipleWriteRequestAsync(
    "*************", 502, unitId: 1, startAddress: 300,
    values: new ushort[] { 1, 0, 1, 1, 0 }, isCoils: true);

Console.WriteLine("Write指令已插入佇列，將在輪詢間隙執行");
```

## 🔗 多連線支援

```csharp
using var modbusMaster = new ModbusMasterBuilder(logger)
    .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
    .AddDevice("*************", 502)
        .WithMultipleConnections(3) // 建立3個連線到同一端點
        .WithParallelProcessing(true, maxParallelRequests: 50)
        .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 100,
            pollingInterval: TimeSpan.FromSeconds(1))
        .PollInputRegisters(unitId: 2, startAddress: 0, count: 50,
            pollingInterval: TimeSpan.FromSeconds(2))
    .EndDevice()
    .AddDevice("*************", 502)
        .WithMultipleConnections(2) // 建立2個連線
        .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 200,
            pollingInterval: TimeSpan.FromMilliseconds(500))
    .EndDevice()
    .Build();

await modbusMaster.StartAsync();

// 查看連線狀態
var connections100 = modbusMaster.GetConnections("*************", 502);
Console.WriteLine($"端點 *************:502 有 {connections100.Count} 個連線:");
foreach (var conn in connections100)
{
    Console.WriteLine($"  連線ID: {conn.GetConnectionKey()}");
}

var connections101 = modbusMaster.GetConnections("*************", 502);
Console.WriteLine($"端點 *************:502 有 {connections101.Count} 個連線:");
foreach (var conn in connections101)
{
    Console.WriteLine($"  連線ID: {conn.GetConnectionKey()}");
}

// 動態建立新連線
var newConnection = modbusMaster.CreateConnection("*************", 502, "extra");
Console.WriteLine($"建立新連線: {newConnection.GetConnectionKey()}");

// 移除連線
bool removed = modbusMaster.RemoveConnection(newConnection);
Console.WriteLine($"移除連線結果: {removed}");
```

## 📈 系統監控功能

```csharp
using Ngp.Communication.ModbusTcpMaster.Monitoring;

// 建立監控服務
var monitoringLogger = loggerFactory.CreateLogger<MonitoringService>();
using var monitoring = new MonitoringService(modbusMaster, monitoringLogger);

// 或使用介面
IMonitoringService monitoringService = monitoring;

// 快速系統摘要
string summary = monitoring.GetQuickSystemSummary();
Console.WriteLine(summary);
// 輸出: Status: Healthy | Endpoints: 2 | Slaves: 3 | Requests: 1250 | Errors: 2 | Memory: 45.2MB | Uptime: 01:23:45

// 系統健康狀態
var healthStatus = monitoring.GetSystemHealthStatus();
Console.WriteLine($"系統健康狀態: {healthStatus}"); // Healthy, Warning, Critical, Unknown

// 連接的端點列表
var connectedEndpoints = monitoring.GetConnectedEndpoints();
foreach (var endpoint in connectedEndpoints)
{
    Console.WriteLine($"已連接端點: {endpoint}");

    // 端點摘要
    var parts = endpoint.Split(':');
    if (parts.Length == 2 && int.TryParse(parts[1], out var port))
    {
        string endpointSummary = monitoring.GetEndpointSummary(parts[0], port);
        Console.WriteLine($"  {endpointSummary}");
    }
}

// 總體統計
Console.WriteLine($"總 Slave 數: {monitoring.GetTotalConfiguredSlaves()}");
Console.WriteLine($"總輪詢範圍數: {monitoring.GetTotalPollingRanges()}");

// 詳細系統資訊
var systemInfo = monitoring.GetSystemMonitoringInfo();
Console.WriteLine($"系統運行時間: {systemInfo.SystemUptime}");
Console.WriteLine($"總請求數: {systemInfo.TotalRequestsSent}");
Console.WriteLine($"記憶體使用: {systemInfo.MemoryUsageMB:F1}MB");

// 端點詳細資訊
var endpointInfo = monitoring.GetEndpointMonitoringInfo("*************", 502);
Console.WriteLine($"端點狀態: {endpointInfo.Status}");
Console.WriteLine($"平均回應時間: {endpointInfo.AverageResponseTimeMs:F2}ms");
Console.WriteLine($"錯誤總數: {endpointInfo.TotalErrors}");

// 性能指標
var metrics = monitoring.GetPerformanceMetrics(TimeSpan.FromMinutes(10));
Console.WriteLine($"每秒請求數: {metrics.RequestsPerSecond:F2}");
Console.WriteLine($"成功率: {metrics.SuccessRatePercent:F1}%");
```

## 🔧 進階功能

### 手動數據轉換

```csharp
using Ngp.Communication.ModbusTcpMaster.Conversion;

// 讀取暫存器
var registers = await modbusMaster.ReadHoldingRegistersAsync("*************", 502, 1, 0, 10);

// 設定轉換配置
var conversions = new[]
{
    new DataConversionConfig
    {
        StartAddress = 0,
        DataType = ModbusDataType.Int16,
        RegisterCount = 1,
        ScaleFactor = 0.1,
        Offset = -40.0,
        Name = "Temperature"
    },
    new DataConversionConfig
    {
        StartAddress = 1,
        DataType = ModbusDataType.Float,
        RegisterCount = 2,
        EndianType = EndianType.BigEndian,
        Name = "Pressure"
    }
};

// 執行轉換
var convertedValues = DataConverter.ConvertMultiple(registers, conversions, baseAddress: 0);

foreach (var converted in convertedValues)
{
    Console.WriteLine($"{converted.Config.Name}: {converted.Value} ({converted.Config.DataType})");
}
```

### 連線管理

```csharp
// 取得連線資訊
var connections = modbusMaster.ConnectionManager.GetConnectionInfo();
foreach (var conn in connections)
{
    Console.WriteLine($"連線: {conn.IpAddress}:{conn.Port} - 狀態: {conn.Status}");
}

// 手動連線
await modbusMaster.ConnectionManager.ConnectAsync("*************", 502);

// 中斷特定連線
await modbusMaster.ConnectionManager.DisconnectAsync("*************", 502);
```

### 輪詢管理

```csharp
// 動態新增輪詢範圍
await modbusMaster.PollingEngine.AddPollingRangeAsync(new RegisterRange
{
    IpAddress = "*************",
    Port = 502,
    UnitId = 1,
    RegisterType = ModbusRegisterType.HoldingRegister,
    StartAddress = 100,
    Count = 20,
    PollingInterval = TimeSpan.FromSeconds(2)
});

// 移除輪詢範圍
await modbusMaster.PollingEngine.RemovePollingRangeAsync("*************", 502, 1,
    ModbusRegisterType.HoldingRegister, 100, 20);

// 取得所有輪詢範圍
var ranges = modbusMaster.PollingEngine.GetPollingRanges("*************", 502);
```

### 端點配置管理

```csharp
// 設定端點特定配置
var endpointConfig = new EndpointConfiguration
{
    Timeout = TimeSpan.FromSeconds(10),
    GapTime = TimeSpan.FromMilliseconds(50),
    ConnectionTimeout = TimeSpan.FromSeconds(8),
    ReceiveTimeout = TimeSpan.FromSeconds(8),
    MaxConcurrentRequestsPerConnection = 5,
    MaxReconnectAttempts = 5,
    ReconnectDelay = TimeSpan.FromSeconds(2),
    EnableParallelProcessing = false,
    MaxParallelRequests = 1
};

modbusMaster.SetEndpointConfiguration("*************", 502, endpointConfig);

// 取得端點配置
var config = modbusMaster.GetEndpointConfiguration("*************", 502);
if (config != null)
{
    Console.WriteLine($"端點 {config.IpAddress}:{config.Port} 的 Timeout: {config.Timeout}");
    Console.WriteLine($"Gap Time: {config.GapTime}");
    Console.WriteLine($"平行處理: {config.EnableParallelProcessing}");
}

// 取得所有端點配置
var allConfigs = modbusMaster.GetAllEndpointConfigurations();
foreach (var kvp in allConfigs)
{
    Console.WriteLine($"端點: {kvp.Key}, Timeout: {kvp.Value.Timeout}");
}

// 移除端點配置
bool removed = modbusMaster.RemoveEndpointConfiguration("*************", 502);
Console.WriteLine($"配置移除結果: {removed}");
```

### 請求佇列管理

```csharp
// 檢查佇列大小
int queueSize = modbusMaster._requestQueueManager.GetQueueSize("*************", 502);
Console.WriteLine($"端點 *************:502 的佇列大小: {queueSize}");

// 取得所有端點的佇列大小
var allQueueSizes = modbusMaster._requestQueueManager.GetAllQueueSizes();
foreach (var kvp in allQueueSizes)
{
    Console.WriteLine($"端點 {kvp.Key} 佇列大小: {kvp.Value}");
}

// 清空特定端點的佇列
modbusMaster._requestQueueManager.ClearQueue("*************", 502);
Console.WriteLine("已清空端點佇列");

// 移除端點佇列
modbusMaster._requestQueueManager.RemoveEndpointQueue("*************", 502);
Console.WriteLine("已移除端點佇列");
```

### 連線識別管理

```csharp
// 建立具有特定ID的連線
var connectionId1 = new ConnectionIdentifier("*************", 502, "primary");
var connectionId2 = new ConnectionIdentifier("*************", 502, "secondary");

// 驗證連線識別
connectionId1.Validate();
connectionId2.Validate();

// 取得連線金鑰
string endpointKey = connectionId1.GetEndpointKey(); // "*************:502"
string connectionKey = connectionId1.GetConnectionKey(); // "*************:502:primary"

// 解析連線金鑰
var parsedId = ConnectionIdentifier.Parse("*************:502:backup");
Console.WriteLine($"解析的連線: {parsedId.IpAddress}:{parsedId.Port}:{parsedId.InstanceId}");

// 建立預設連線
var defaultConn = ConnectionIdentifier.CreateDefault("*************", 502);
Console.WriteLine($"預設連線: {defaultConn.GetConnectionKey()}");

// 建立新連線
var newConn = ConnectionIdentifier.CreateNew("*************", 502);
Console.WriteLine($"新連線: {newConn.GetConnectionKey()}");

// 複製連線識別
var clonedConn = connectionId1.Clone();
Console.WriteLine($"複製的連線: {clonedConn.GetConnectionKey()}");
```

## ⚙️ 配置選項

### 連線設定

```csharp
.WithConnectionSettings(
    connectionTimeout: TimeSpan.FromSeconds(5),      // 連線逾時
    receiveTimeout: TimeSpan.FromSeconds(5),         // 接收逾時
    maxConcurrentRequestsPerConnection: 10,          // 每個連線的最大並發請求數
    maxReconnectAttempts: 3,                         // 最大重連嘗試次數
    reconnectDelay: TimeSpan.FromSeconds(1))         // 重連延遲
```

### 輪詢設定

```csharp
.WithPollingSettings(
    pollingCheckInterval: TimeSpan.FromMilliseconds(100),  // 輪詢檢查間隔
    maxConcurrentPolling: 50,                              // 最大並發輪詢數
    enableChangeDetection: true,                           // 啟用變更偵測
    maxRegisterRangeSize: 100,                            // 最大暫存器範圍大小
    maxCoilRangeSize: 1000)                               // 最大線圈範圍大小
```

### 通訊設定

```csharp
.WithProtocolMode(ModbusProtocolMode.ModbusTcp)     // 協定模式
.WithDefaultTimeout(TimeSpan.FromSeconds(5))        // 預設逾時
.WithGapTime(TimeSpan.FromMilliseconds(10))         // 請求間隔時間
.WithParallelProcessing(true)                       // 啟用平行處理
.WithMaxParallelRequests(100)                       // 最大平行請求數
```

## 📚 支援的 Modbus 功能碼

| 功能碼 | 功能 | 支援狀態 |
|--------|------|----------|
| 01 | Read Coils | ✅ |
| 02 | Read Discrete Inputs | ✅ |
| 03 | Read Holding Registers | ✅ |
| 04 | Read Input Registers | ✅ |
| 05 | Write Single Coil | ✅ |
| 06 | Write Single Register | ✅ |
| 15 | Write Multiple Coils | ✅ |
| 16 | Write Multiple Registers | ✅ |

## 🏗️ 專案結構

```text
Ngp.Communication.ModbusTcpMaster/
├── Engine/                     # 核心引擎
│   ├── ModbusMaster.cs         # 主要 Modbus Master 類別
│   ├── RegisterPollingEngine.cs # 輪詢引擎
│   └── RequestQueueManager.cs  # 請求佇列管理器
├── Connection/                 # 連線管理
│   └── TcpConnectionManager.cs # TCP 連線管理器
├── Models/                     # 資料模型
│   ├── ModbusFunctionCodes.cs  # Modbus 功能碼和列舉
│   ├── ModbusRequest.cs        # Modbus 請求模型
│   ├── ModbusResponse.cs       # Modbus 回應模型
│   ├── RegisterRange.cs        # 暫存器範圍模型
│   ├── DataTypes.cs            # 數據轉換類型定義
│   ├── EndpointConfiguration.cs # 端點配置模型
│   ├── ConnectionIdentifier.cs # 連線識別模型
│   └── RequestPriority.cs      # 請求優先級模型
├── Conversion/                 # 數據轉換
│   └── DataConverter.cs        # 數據轉換器
├── Monitoring/                 # 狀態監控
│   ├── MonitoringModels.cs     # 監控資料模型
│   └── MonitoringService.cs    # 監控服務
├── Events/                     # 事件系統
│   └── ModbusEventArgs.cs      # 事件參數定義
├── Fluent/                     # FluentAPI
│   ├── ModbusMasterBuilder.cs  # 主要建構器
│   └── DeviceConfigurationBuilder.cs # 裝置配置建構器
├── Exceptions/                 # 例外處理
│   └── ModbusExceptions.cs     # Modbus 相關例外
└── Examples/                   # 使用範例
    ├── BasicExample.cs         # 基本使用範例
    ├── PollingExample.cs       # 輪詢範例
    ├── DataConversionExample.cs # 數據轉換範例
    └── MonitoringExample.cs    # 監控範例
```

## 🧪 測試

專案包含完整的單元測試：

```bash
# 執行所有測試
dotnet test

# 執行特定測試類別
dotnet test --filter "TestClass=ModbusMasterTests"

# 產生測試覆蓋率報告
dotnet test --collect:"XPlat Code Coverage"
```

## 📄 授權

本專案採用 MIT 授權條款。詳見 [LICENSE](LICENSE) 檔案。

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request！

## 📞 支援

如有問題或建議，請透過以下方式聯繫：

- 建立 [GitHub Issue](https://github.com/your-repo/issues)
- 發送電子郵件至 <<EMAIL>>

---

**Ngp.Communication.ModbusTcpMaster** - 讓 Modbus 通訊變得簡單而強大！ 🚀
