# Ngp.Communication.ModbusTcpMaster

企業級 Modbus TCP Master 庫，使用 .NET 9 開發，提供高性能、穩定可靠的 Modbus TCP 通訊功能。

## 主要特性

### 🚀 高性能
- 支援平行化處理，可同時連接 1000+ 台 Slave 裝置
- 智慧連線管理，一個 IP:Port 組合使用單一連線
- 可調整的並發請求數量和輪詢設定
- 高效能的 TCP 連線池管理

### 🔧 完整的 Modbus 支援
- 支援 Modbus TCP 和 Modbus RTU over TCP
- 實作所有標準 Modbus 功能碼
- 支援 Single Write 和 Multiple Write 操作
- 自動處理 Slave 回傳的錯誤碼

### 🛡️ 企業級穩定性
- 強健的錯誤處理和重連機制
- Thread-Safe 設計
- 完善的資源管理和 Graceful Exit
- 可調整的 Timeout 和 Gap 時間

### 📊 智慧輪詢引擎
- 自動分割過長的暫存器請求範圍
- 變更偵測機制，只在數值改變時觸發事件
- 可設定的輪詢間隔和範圍限制
- 平行化輪詢處理

### 🎯 易用的 API
- FluentAPI 設計模式
- 完整的事件系統
- 支援 Minimal API 整合
- 豐富的設定選項

### 🔄 智慧數據轉換
- 支援多種數據類型轉換 (Int16/32/64, UInt16/32/64, Float, Double, Boolean, String)
- 四種 Endian 排列組合 (BigEndian, LittleEndian, BigEndianWordSwap, LittleEndianWordSwap)
- 自動數據轉換並傳送到委派事件
- 支援縮放因子和偏移量
- 位元提取和字串編碼轉換

### 📊 企業級日誌與監控
- 使用 Microsoft.Extensions.Logging 進行結構化日誌記錄
- 支援不同日誌等級 (Trace, Debug, Information, Warning, Error, Critical)
- 即時系統狀態監控和性能指標
- 詳細的端點和 Slave 統計資訊
- 錯誤分析和回應時間追蹤

## 快速開始

### 基本使用

```csharp
using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Fluent;

// 建立 ModbusMaster
using var modbusMaster = new ModbusMasterBuilder()
    .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
    .WithDefaultTimeout(TimeSpan.FromSeconds(5))
    .WithParallelProcessing(true)
    .Build();

// 啟動
await modbusMaster.StartAsync();

// 讀取 Holding Registers
var registers = await modbusMaster.ReadHoldingRegistersAsync(
    "*************", 502, unitId: 1, startAddress: 0, quantity: 10);

// 寫入單一暫存器
await modbusMaster.WriteSingleRegisterAsync(
    "*************", 502, unitId: 1, address: 0, value: 1234);

// 停止
await modbusMaster.StopAsync();
```

### 數據轉換功能

```csharp
// 自動數據轉換與輪詢
using var modbusMaster = new ModbusMasterBuilder()
    .WithPollingSettings(enableChangeDetection: true)
    .AddDevice("*************", 502)
        .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 20,
            pollingInterval: TimeSpan.FromSeconds(1))
            // 溫度感測器 (16位元整數，縮放0.1，偏移-40)
            .WithDataConversion(0, ModbusDataType.Int16, EndianType.BigEndian, 0.1, -40.0, "Temperature")
            // 壓力感測器 (32位元浮點數，Big Endian)
            .WithDataConversion(1, ModbusDataType.Float, EndianType.BigEndian, name: "Pressure")
            // 計數器 (32位元無符號整數，Little Endian Word Swap)
            .WithDataConversion(3, ModbusDataType.UInt32, EndianType.LittleEndianWordSwap, name: "Counter")
            // 警報位元
            .WithBooleanConversion(5, bitPosition: 0, name: "Alarm1")
            .WithBooleanConversion(5, bitPosition: 1, name: "Alarm2")
            // 裝置名稱字串
            .WithStringConversion(10, length: 8, encodingName: "ASCII", name: "DeviceName")
        .EndDevice()
    .OnDataValueUpdated((sender, e) =>
    {
        // 處理轉換後的數據
        foreach (var converted in e.ConvertedValues)
        {
            Console.WriteLine($"{converted.Config.Name}: {converted.Value} ({converted.Config.DataType})");
        }
    })
    .Build();

await modbusMaster.StartAsync();
```

### 自動輪詢設定

```csharp
using var modbusMaster = new ModbusMasterBuilder()
    .WithPollingSettings(
        pollingCheckInterval: TimeSpan.FromMilliseconds(100),
        enableChangeDetection: true)
    .AddDevice("*************", 502)
        .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 10, 
            pollingInterval: TimeSpan.FromSeconds(1))
        .PollCoils(unitId: 1, startAddress: 0, count: 16, 
            pollingInterval: TimeSpan.FromSeconds(2))
        .EndDevice()
    .OnDataValueUpdated((sender, e) =>
    {
        Console.WriteLine($"數值更新: {e.RegisterType} 位址:{e.StartAddress}");
    })
    .Build();

await modbusMaster.StartAsync();
```

### 事件處理

```csharp
var modbusMaster = new ModbusMasterBuilder()
    .OnDataValueUpdated((sender, e) =>
    {
        // 處理數值更新事件
        Console.WriteLine($"數值更新: {e.IpAddress}:{e.Port}");
    })
    .OnConnectionStatusChanged((sender, e) =>
    {
        // 處理連線狀態變更
        Console.WriteLine($"連線狀態: {e.CurrentStatus}");
    })
    .OnCommunicationError((sender, e) =>
    {
        // 處理通訊錯誤
        Console.WriteLine($"通訊錯誤: {e.ErrorMessage}");
    })
    .OnModbusException((sender, e) =>
    {
        // 處理 Modbus 例外
        Console.WriteLine($"Modbus 例外: {e.ExceptionCode}");
    })
    .Build();
```

## API 參考

### 讀取操作

```csharp
// 讀取 Coils
bool[] coils = await modbusMaster.ReadCoilsAsync(ip, port, unitId, startAddress, quantity);

// 讀取 Discrete Inputs
bool[] inputs = await modbusMaster.ReadDiscreteInputsAsync(ip, port, unitId, startAddress, quantity);

// 讀取 Holding Registers
ushort[] holdings = await modbusMaster.ReadHoldingRegistersAsync(ip, port, unitId, startAddress, quantity);

// 讀取 Input Registers
ushort[] inputs = await modbusMaster.ReadInputRegistersAsync(ip, port, unitId, startAddress, quantity);
```

### 寫入操作

```csharp
// 寫入單一 Coil
await modbusMaster.WriteSingleCoilAsync(ip, port, unitId, address, value);

// 寫入單一暫存器
await modbusMaster.WriteSingleRegisterAsync(ip, port, unitId, address, value);

// 寫入多個 Coils
await modbusMaster.WriteMultipleCoilsAsync(ip, port, unitId, startAddress, values);

// 寫入多個暫存器
await modbusMaster.WriteMultipleRegistersAsync(ip, port, unitId, startAddress, values);
```

### 輪詢管理

```csharp
// 新增輪詢範圍
var range = new RegisterRange
{
    RegisterType = ModbusRegisterType.HoldingRegister,
    UnitId = 1,
    StartAddress = 0,
    Count = 10,
    PollingInterval = TimeSpan.FromSeconds(1)
};
modbusMaster.AddPollingRange(ip, port, range);

// 移除輪詢範圍
modbusMaster.RemovePollingRange(ip, port, range);
```

### 連線管理

```csharp
// 取得連線資訊
var connections = modbusMaster.ConnectionManager.GetConnectionInfo();

// 取得連線狀態
var status = modbusMaster.ConnectionManager.GetConnectionStatus(ip, port);

// 中斷特定連線
await modbusMaster.ConnectionManager.DisconnectAsync(ip, port);
```

### 系統監控 (Class Library 介面)

```csharp
// 建立監控服務
var logger = serviceProvider.GetRequiredService<ILogger<MonitoringService>>();
var monitoringService = new MonitoringService(modbusMaster, logger);

// 或使用介面
IMonitoringService monitoring = monitoringService;

// 快速系統摘要
string summary = monitoring.GetQuickSystemSummary();
Console.WriteLine(summary);
// 輸出: Status: Healthy | Endpoints: 2 | Slaves: 3 | Requests: 1250 | Errors: 2 | Memory: 45.2MB | Uptime: 01:23:45

// 系統健康狀態
var healthStatus = monitoring.GetSystemHealthStatus();
Console.WriteLine($"系統健康狀態: {healthStatus}"); // Healthy, Warning, Critical, Unknown

// 連接的端點列表
var connectedEndpoints = monitoring.GetConnectedEndpoints();
foreach (var endpoint in connectedEndpoints)
{
    Console.WriteLine($"已連接端點: {endpoint}");
}

// 總體統計
Console.WriteLine($"總 Slave 數: {monitoring.GetTotalConfiguredSlaves()}");
Console.WriteLine($"總輪詢範圍數: {monitoring.GetTotalPollingRanges()}");

// 詳細系統資訊
var systemInfo = monitoring.GetSystemMonitoringInfo();
Console.WriteLine($"系統運行時間: {systemInfo.SystemUptime}");
Console.WriteLine($"總請求數: {systemInfo.TotalRequestsSent}");
Console.WriteLine($"記憶體使用: {systemInfo.MemoryUsageMB:F1}MB");

// 端點詳細資訊
var endpointInfo = monitoring.GetEndpointMonitoringInfo("*************", 502);
Console.WriteLine($"端點狀態: {endpointInfo.Status}");
Console.WriteLine($"平均回應時間: {endpointInfo.AverageResponseTimeMs:F2}ms");
Console.WriteLine($"錯誤總數: {endpointInfo.TotalErrors}");

// 端點摘要
string endpointSummary = monitoring.GetEndpointSummary("*************", 502);
Console.WriteLine(endpointSummary);
// 輸出: Endpoint *************:502 | Status: Connected | Slaves: 2 | Ranges: 5 | Registers: 150 | Avg Response: 12.34ms | Errors: 1

// 性能指標
var metrics = monitoring.GetPerformanceMetrics(TimeSpan.FromMinutes(10));
Console.WriteLine($"每秒請求數: {metrics.RequestsPerSecond:F2}");
Console.WriteLine($"成功率: {metrics.SuccessRatePercent:F1}%");
```

### 數據轉換

```csharp
// 手動數據轉換
var registers = await modbusMaster.ReadHoldingRegistersAsync(ip, port, unitId, 0, 10);

var conversions = new[]
{
    new DataConversionConfig
    {
        StartAddress = 0,
        DataType = ModbusDataType.Int16,
        RegisterCount = 1,
        ScaleFactor = 0.1,
        Offset = -40.0,
        Name = "Temperature"
    },
    new DataConversionConfig
    {
        StartAddress = 1,
        DataType = ModbusDataType.Float,
        RegisterCount = 2,
        EndianType = EndianType.BigEndian,
        Name = "Pressure"
    }
};

var convertedValues = DataConverter.ConvertMultiple(registers, conversions, baseAddress: 0);

foreach (var converted in convertedValues)
{
    Console.WriteLine($"{converted.Config.Name}: {converted.Value} ({converted.Config.DataType})");
}
```

## 設定選項

### 連線設定

```csharp
.WithConnectionSettings(
    connectionTimeout: TimeSpan.FromSeconds(5),      // 連線逾時
    receiveTimeout: TimeSpan.FromSeconds(5),         // 接收逾時
    maxConcurrentRequestsPerConnection: 10,          // 每連線最大並發請求數
    maxReconnectAttempts: 3,                         // 最大重連次數
    reconnectDelay: TimeSpan.FromSeconds(1))         // 重連延遲
```

### 輪詢設定

```csharp
.WithPollingSettings(
    pollingCheckInterval: TimeSpan.FromMilliseconds(100),  // 輪詢檢查間隔
    maxConcurrentPolling: 50,                              // 最大並發輪詢數
    enableChangeDetection: true,                           // 啟用變更偵測
    maxRegisterRangeSize: 100,                            // 最大暫存器範圍大小
    maxCoilRangeSize: 1000)                               // 最大 Coil 範圍大小
```

### 效能設定

```csharp
.WithParallelProcessing(true)                    // 啟用平行處理
.WithMaxParallelRequests(100)                    // 最大平行請求數
.WithGapTime(TimeSpan.FromMilliseconds(10))      // 請求間隔時間
.WithDefaultTimeout(TimeSpan.FromSeconds(5))     // 預設逾時時間
```

## Minimal API 整合

```csharp
// 註冊服務
builder.Services.AddSingleton<ModbusMaster>(serviceProvider =>
{
    return new ModbusMasterBuilder()
        .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
        .WithParallelProcessing(true)
        .Build();
});

// 直接使用 ModbusMaster
var values = await modbusMaster.ReadHoldingRegistersAsync("*************", 502, 1, 0, 10);

// 直接使用監控服務
var systemInfo = monitoringService.GetSystemMonitoringInfo();
var endpointInfo = monitoringService.GetEndpointMonitoringInfo("*************", 502);
```

## 錯誤處理

庫提供了完整的例外處理機制：

- `ModbusException`: 基礎 Modbus 例外
- `ModbusSlaveException`: Slave 裝置回傳的例外
- `ModbusTimeoutException`: 逾時例外
- `ModbusConnectionException`: 連線例外
- `ModbusInvalidResponseException`: 無效回應例外
- `ModbusCrcException`: CRC 錯誤例外

## 效能考量

- 建議每個 IP:Port 組合使用單一連線
- 適當設定並發請求數量以避免網路壅塞
- 使用變更偵測減少不必要的事件觸發
- 合理設定輪詢間隔以平衡即時性和效能


