namespace Ngp.Communication.ModbusTcpMaster.Examples;

using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Fluent;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Conversion;

/// <summary>
/// Example demonstrating data conversion functionality with different data types and endian configurations
/// </summary>
public class DataConversionExample
{
    /// <summary>
    /// Example of manual data conversion
    /// </summary>
    public static async Task ManualConversionExample()
    {
        Console.WriteLine("=== Manual Data Conversion Example ===");
        
        using var modbusMaster = new ModbusMasterBuilder()
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .WithDefaultTimeout(TimeSpan.FromSeconds(5))
            .Build();

        try
        {
            await modbusMaster.StartAsync();

            var deviceIp = "*************";
            var devicePort = 502;
            byte unitId = 1;

            // Read some registers
            Console.WriteLine("Reading registers for conversion...");
            var registers = await modbusMaster.ReadHoldingRegistersAsync(
                deviceIp, devicePort, unitId, startAddress: 0, quantity: 10);
            
            Console.WriteLine($"Raw register values: [{string.Join(", ", registers)}]");

            // Create different conversion configurations
            var conversions = new[]
            {
                // 16-bit signed integer at address 0
                new DataConversionConfig
                {
                    StartAddress = 0,
                    DataType = ModbusDataType.Int16,
                    Name = "Temperature",
                    ScaleFactor = 0.1,
                    Offset = -40.0
                },
                
                // 32-bit float at address 1-2 (Big Endian)
                new DataConversionConfig
                {
                    StartAddress = 1,
                    DataType = ModbusDataType.Float,
                    EndianType = EndianType.BigEndian,
                    Name = "Pressure_BE"
                },
                
                // 32-bit float at address 1-2 (Little Endian)
                new DataConversionConfig
                {
                    StartAddress = 1,
                    DataType = ModbusDataType.Float,
                    EndianType = EndianType.LittleEndian,
                    Name = "Pressure_LE"
                },
                
                // 32-bit float at address 1-2 (Big Endian Word Swap)
                new DataConversionConfig
                {
                    StartAddress = 1,
                    DataType = ModbusDataType.Float,
                    EndianType = EndianType.BigEndianWordSwap,
                    Name = "Pressure_BEWS"
                },
                
                // 32-bit unsigned integer at address 3-4
                new DataConversionConfig
                {
                    StartAddress = 3,
                    DataType = ModbusDataType.UInt32,
                    EndianType = EndianType.BigEndian,
                    Name = "Counter"
                },
                
                // Boolean bits from address 5
                new DataConversionConfig
                {
                    StartAddress = 5,
                    DataType = ModbusDataType.Boolean,
                    BitPosition = 0,
                    Name = "Alarm_Bit0"
                },
                
                new DataConversionConfig
                {
                    StartAddress = 5,
                    DataType = ModbusDataType.Boolean,
                    BitPosition = 7,
                    Name = "Alarm_Bit7"
                },
                
                // String at address 6-9 (8 bytes = 4 registers)
                new DataConversionConfig
                {
                    StartAddress = 6,
                    DataType = ModbusDataType.String,
                    RegisterCount = 4,
                    EndianType = EndianType.BigEndian,
                    StringEncodingName = "ASCII",
                    Name = "DeviceName"
                }
            };

            // Convert the data
            var convertedValues = DataConverter.ConvertMultiple(registers, conversions, baseAddress: 0);

            Console.WriteLine("\nConverted values:");
            foreach (var converted in convertedValues)
            {
                Console.WriteLine($"  {converted.Config.Name}: {converted.Value} " +
                    $"({converted.Config.DataType}, {converted.Config.EndianType}) " +
                    $"[Quality: {converted.Quality}]");
                
                if (!string.IsNullOrEmpty(converted.ErrorMessage))
                {
                    Console.WriteLine($"    Error: {converted.ErrorMessage}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
        finally
        {
            await modbusMaster.StopAsync();
        }
    }

    /// <summary>
    /// Example of automatic data conversion with polling
    /// </summary>
    public static async Task AutomaticConversionExample()
    {
        Console.WriteLine("\n=== Automatic Data Conversion with Polling Example ===");
        
        using var modbusMaster = new ModbusMasterBuilder()
            .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
            .WithPollingSettings(enableChangeDetection: true)
            .AddDevice("*************", 502)
                .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 20, 
                    pollingInterval: TimeSpan.FromSeconds(2))
                    // Add data conversions to the polling range
                    .WithDataConversion(0, ModbusDataType.Int16, EndianType.BigEndian, 0.1, -40.0, "Temperature")
                    .WithDataConversion(1, ModbusDataType.Float, EndianType.BigEndian, name: "Pressure")
                    .WithDataConversion(3, ModbusDataType.UInt32, EndianType.BigEndian, name: "Counter")
                    .WithBooleanConversion(5, bitPosition: 0, name: "Alarm1")
                    .WithBooleanConversion(5, bitPosition: 1, name: "Alarm2")
                    .WithStringConversion(10, length: 8, name: "DeviceID")
                .EndDevice()
            .OnDataValueUpdated((sender, e) =>
            {
                Console.WriteLine($"\n[{DateTime.Now:HH:mm:ss.fff}] Data updated from {e.IpAddress}:{e.Port}");
                Console.WriteLine($"  Raw registers: [{string.Join(", ", e.RegisterValues ?? Array.Empty<ushort>())}]");
                
                foreach (var converted in e.ConvertedValues)
                {
                    Console.WriteLine($"  {converted.Config.Name}: {converted.Value} " +
                        $"({converted.Config.DataType}) [Quality: {converted.Quality}]");
                }
            })
            .Build();

        try
        {
            Console.WriteLine("Starting automatic polling with data conversion...");
            await modbusMaster.StartAsync();

            Console.WriteLine("Polling for 30 seconds... Press any key to stop early.");
            var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(30));
            
            while (!cancellationTokenSource.Token.IsCancellationRequested)
            {
                if (Console.KeyAvailable)
                {
                    Console.ReadKey();
                    break;
                }
                await Task.Delay(100);
            }

            Console.WriteLine("\nStopping polling...");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
        finally
        {
            await modbusMaster.StopAsync();
        }
    }

    /// <summary>
    /// Example demonstrating different endian configurations
    /// </summary>
    public static void EndianConversionExample()
    {
        Console.WriteLine("\n=== Endian Conversion Example ===");
        
        // Simulate register data for a 32-bit float value (3.14159)
        var floatBytes = BitConverter.GetBytes(3.14159f);
        var registers = new ushort[]
        {
            (ushort)((floatBytes[1] << 8) | floatBytes[0]),  // Register 0
            (ushort)((floatBytes[3] << 8) | floatBytes[2])   // Register 1
        };
        
        Console.WriteLine($"Original float value: 3.14159");
        Console.WriteLine($"Register values: [{string.Join(", ", registers)}]");
        Console.WriteLine($"Raw bytes: [{string.Join(", ", floatBytes.Select(b => $"0x{b:X2}"))}]");

        var endianTypes = new[]
        {
            EndianType.BigEndian,
            EndianType.LittleEndian,
            EndianType.BigEndianWordSwap,
            EndianType.LittleEndianWordSwap
        };

        foreach (var endianType in endianTypes)
        {
            var config = new DataConversionConfig
            {
                StartAddress = 0,
                DataType = ModbusDataType.Float,
                EndianType = endianType,
                Name = $"Float_{endianType}"
            };

            var result = DataConverter.ConvertRegisters(registers, config);
            Console.WriteLine($"  {endianType}: {result.Value} [Quality: {result.Quality}]");
        }
    }

    /// <summary>
    /// Example demonstrating various data type conversions
    /// </summary>
    public static void DataTypeConversionExample()
    {
        Console.WriteLine("\n=== Data Type Conversion Example ===");
        
        // Create test data
        var registers = new ushort[] { 1000, 2000, 3000, 4000, 0x5555, 0xAAAA };
        
        Console.WriteLine($"Test registers: [{string.Join(", ", registers)}]");

        var conversions = new[]
        {
            new DataConversionConfig { StartAddress = 0, DataType = ModbusDataType.Int16, Name = "Int16_Raw" },
            new DataConversionConfig { StartAddress = 0, DataType = ModbusDataType.Int16, ScaleFactor = 0.1, Name = "Int16_Scaled" },
            new DataConversionConfig { StartAddress = 0, DataType = ModbusDataType.UInt16, Name = "UInt16" },
            new DataConversionConfig { StartAddress = 0, DataType = ModbusDataType.Int32, EndianType = EndianType.BigEndian, Name = "Int32_BE" },
            new DataConversionConfig { StartAddress = 0, DataType = ModbusDataType.UInt32, EndianType = EndianType.LittleEndian, Name = "UInt32_LE" },
            new DataConversionConfig { StartAddress = 4, DataType = ModbusDataType.Boolean, BitPosition = 0, Name = "Bit0" },
            new DataConversionConfig { StartAddress = 4, DataType = ModbusDataType.Boolean, BitPosition = 8, Name = "Bit8" },
            new DataConversionConfig { StartAddress = 4, DataType = ModbusDataType.Boolean, BitPosition = 15, Name = "Bit15" }
        };

        var results = DataConverter.ConvertMultiple(registers, conversions, baseAddress: 0);

        Console.WriteLine("\nConversion results:");
        foreach (var result in results)
        {
            Console.WriteLine($"  {result.Config.Name}: {result.Value} ({result.Config.DataType})");
        }
    }

    /// <summary>
    /// Run all data conversion examples
    /// </summary>
    public static async Task RunAllExamples()
    {
        Console.WriteLine("=== Data Conversion Examples ===\n");
        
        EndianConversionExample();
        DataTypeConversionExample();
        
        // Note: These examples require actual Modbus devices
        // await ManualConversionExample();
        // await AutomaticConversionExample();
        
        Console.WriteLine("\n=== Data Conversion Examples Completed ===");
    }
}
