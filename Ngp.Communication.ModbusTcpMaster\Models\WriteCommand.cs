namespace Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Represents a write command that can be queued for execution
/// </summary>
public class WriteCommand
{
    /// <summary>
    /// Unique identifier for the write command
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();
    
    /// <summary>
    /// IP address of the target device
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;
    
    /// <summary>
    /// Port number of the target device
    /// </summary>
    public int Port { get; set; }
    
    /// <summary>
    /// Unit identifier (slave address)
    /// </summary>
    public byte UnitId { get; set; }
    
    /// <summary>
    /// Function code for the write operation
    /// </summary>
    public ModbusFunctionCode FunctionCode { get; set; }
    
    /// <summary>
    /// Starting address for the write operation
    /// </summary>
    public ushort StartAddress { get; set; }
    
    /// <summary>
    /// Data to write
    /// </summary>
    public byte[] Data { get; set; } = Array.Empty<byte>();
    
    /// <summary>
    /// Number of registers/coils to write
    /// </summary>
    public ushort Quantity { get; set; }
    
    /// <summary>
    /// Timeout for the write operation
    /// </summary>
    public TimeSpan? Timeout { get; set; }
    
    /// <summary>
    /// Priority of the write command (higher values = higher priority)
    /// </summary>
    public int Priority { get; set; } = 0;
    
    /// <summary>
    /// Timestamp when the command was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// Timestamp when the command was executed
    /// </summary>
    public DateTime? ExecutedAt { get; set; }
    
    /// <summary>
    /// Task completion source for async execution
    /// </summary>
    public TaskCompletionSource<bool> CompletionSource { get; set; } = new();
    
    /// <summary>
    /// Cancellation token for the operation
    /// </summary>
    public CancellationToken CancellationToken { get; set; }
    
    /// <summary>
    /// Create a write single coil command
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="address">Coil address</param>
    /// <param name="value">Coil value</param>
    /// <param name="timeout">Timeout</param>
    /// <param name="priority">Priority</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write command</returns>
    public static WriteCommand CreateWriteSingleCoil(string ipAddress, int port, byte unitId, ushort address, bool value, TimeSpan? timeout = null, int priority = 0, CancellationToken cancellationToken = default)
    {
        return new WriteCommand
        {
            IpAddress = ipAddress,
            Port = port,
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.WriteSingleCoil,
            StartAddress = address,
            Data = new byte[] { (byte)(value ? 0xFF : 0x00), 0x00 },
            Quantity = 1,
            Timeout = timeout,
            Priority = priority,
            CancellationToken = cancellationToken
        };
    }
    
    /// <summary>
    /// Create a write single register command
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="address">Register address</param>
    /// <param name="value">Register value</param>
    /// <param name="timeout">Timeout</param>
    /// <param name="priority">Priority</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write command</returns>
    public static WriteCommand CreateWriteSingleRegister(string ipAddress, int port, byte unitId, ushort address, ushort value, TimeSpan? timeout = null, int priority = 0, CancellationToken cancellationToken = default)
    {
        var data = BitConverter.GetBytes(value);
        if (BitConverter.IsLittleEndian)
        {
            Array.Reverse(data);
        }
        
        return new WriteCommand
        {
            IpAddress = ipAddress,
            Port = port,
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.WriteSingleRegister,
            StartAddress = address,
            Data = data,
            Quantity = 1,
            Timeout = timeout,
            Priority = priority,
            CancellationToken = cancellationToken
        };
    }
    
    /// <summary>
    /// Create a write multiple coils command
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Coil values</param>
    /// <param name="timeout">Timeout</param>
    /// <param name="priority">Priority</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write command</returns>
    public static WriteCommand CreateWriteMultipleCoils(string ipAddress, int port, byte unitId, ushort startAddress, bool[] values, TimeSpan? timeout = null, int priority = 0, CancellationToken cancellationToken = default)
    {
        var data = ConvertCoilsToBytes(values);
        
        return new WriteCommand
        {
            IpAddress = ipAddress,
            Port = port,
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.WriteMultipleCoils,
            StartAddress = startAddress,
            Data = data,
            Quantity = (ushort)values.Length,
            Timeout = timeout,
            Priority = priority,
            CancellationToken = cancellationToken
        };
    }
    
    /// <summary>
    /// Create a write multiple registers command
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="unitId">Unit identifier</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Register values</param>
    /// <param name="timeout">Timeout</param>
    /// <param name="priority">Priority</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write command</returns>
    public static WriteCommand CreateWriteMultipleRegisters(string ipAddress, int port, byte unitId, ushort startAddress, ushort[] values, TimeSpan? timeout = null, int priority = 0, CancellationToken cancellationToken = default)
    {
        var data = ConvertRegistersToBytes(values);
        
        return new WriteCommand
        {
            IpAddress = ipAddress,
            Port = port,
            UnitId = unitId,
            FunctionCode = ModbusFunctionCode.WriteMultipleRegisters,
            StartAddress = startAddress,
            Data = data,
            Quantity = (ushort)values.Length,
            Timeout = timeout,
            Priority = priority,
            CancellationToken = cancellationToken
        };
    }
    
    /// <summary>
    /// Convert coil values to byte array
    /// </summary>
    /// <param name="values">Coil values</param>
    /// <returns>Byte array</returns>
    private static byte[] ConvertCoilsToBytes(bool[] values)
    {
        var byteCount = (values.Length + 7) / 8;
        var bytes = new byte[byteCount];

        for (int i = 0; i < values.Length; i++)
        {
            if (values[i])
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                bytes[byteIndex] |= (byte)(1 << bitIndex);
            }
        }

        return bytes;
    }
    
    /// <summary>
    /// Convert register values to byte array
    /// </summary>
    /// <param name="values">Register values</param>
    /// <returns>Byte array</returns>
    private static byte[] ConvertRegistersToBytes(ushort[] values)
    {
        var bytes = new byte[values.Length * 2];

        for (int i = 0; i < values.Length; i++)
        {
            var registerBytes = BitConverter.GetBytes(values[i]);
            if (BitConverter.IsLittleEndian)
            {
                bytes[i * 2] = registerBytes[1];
                bytes[i * 2 + 1] = registerBytes[0];
            }
            else
            {
                bytes[i * 2] = registerBytes[0];
                bytes[i * 2 + 1] = registerBytes[1];
            }
        }

        return bytes;
    }
    
    /// <summary>
    /// Get the endpoint key for this command
    /// </summary>
    /// <returns>Endpoint key</returns>
    public string GetEndpointKey()
    {
        return $"{IpAddress}:{Port}";
    }
    
    /// <summary>
    /// String representation of the write command
    /// </summary>
    /// <returns>String representation</returns>
    public override string ToString()
    {
        return $"WriteCommand[{Id:N}]: {FunctionCode} to {IpAddress}:{Port} Unit:{UnitId} Addr:{StartAddress} Qty:{Quantity} Priority:{Priority}";
    }
}
