namespace Ngp.Communication.ModbusTcpMaster.Connection;

using System.Net.Sockets;
using System.Collections.Concurrent;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Exceptions;
using Ngp.Communication.ModbusTcpMaster.Events;

/// <summary>
/// Represents a single TCP connection to a Modbus device
/// </summary>
public class ModbusConnection : IDisposable
{
    private readonly string _ipAddress;
    private readonly int _port;
    private readonly object _lockObject = new();
    private readonly ConcurrentDictionary<ushort, TaskCompletionSource<ModbusResponse>> _pendingRequests = new();
    private readonly SemaphoreSlim _sendSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    
    private TcpClient? _tcpClient;
    private NetworkStream? _networkStream;
    private Task? _receiveTask;
    private ConnectionStatus _status = ConnectionStatus.Disconnected;
    private DateTime _lastActivity = DateTime.UtcNow;
    private int _reconnectAttempts = 0;
    private bool _disposed = false;
    
    /// <summary>
    /// Maximum number of concurrent requests
    /// </summary>
    public int MaxConcurrentRequests { get; set; } = 10;
    
    /// <summary>
    /// Connection timeout
    /// </summary>
    public TimeSpan ConnectionTimeout { get; set; } = TimeSpan.FromSeconds(5);
    
    /// <summary>
    /// Receive timeout
    /// </summary>
    public TimeSpan ReceiveTimeout { get; set; } = TimeSpan.FromSeconds(5);
    
    /// <summary>
    /// Maximum reconnection attempts
    /// </summary>
    public int MaxReconnectAttempts { get; set; } = 3;
    
    /// <summary>
    /// Delay between reconnection attempts
    /// </summary>
    public TimeSpan ReconnectDelay { get; set; } = TimeSpan.FromSeconds(1);
    
    /// <summary>
    /// Current connection status
    /// </summary>
    public ConnectionStatus Status
    {
        get => _status;
        private set
        {
            var previousStatus = _status;
            _status = value;
            if (previousStatus != value)
            {
                StatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs
                {
                    IpAddress = _ipAddress,
                    Port = _port,
                    PreviousStatus = previousStatus,
                    CurrentStatus = value,
                    Timestamp = DateTime.UtcNow
                });
            }
        }
    }
    
    /// <summary>
    /// IP address of the device
    /// </summary>
    public string IpAddress => _ipAddress;
    
    /// <summary>
    /// Port number
    /// </summary>
    public int Port => _port;
    
    /// <summary>
    /// Indicates if the connection is active
    /// </summary>
    public bool IsConnected => Status == ConnectionStatus.Connected && 
                              _tcpClient?.Connected == true;
    
    /// <summary>
    /// Last activity timestamp
    /// </summary>
    public DateTime LastActivity => _lastActivity;
    
    /// <summary>
    /// Number of pending requests
    /// </summary>
    public int PendingRequestCount => _pendingRequests.Count;
    
    /// <summary>
    /// Event raised when connection status changes
    /// </summary>
    public event EventHandler<ConnectionStatusChangedEventArgs>? StatusChanged;
    
    /// <summary>
    /// Event raised when a communication error occurs
    /// </summary>
    public event EventHandler<CommunicationErrorEventArgs>? CommunicationError;
    
    public ModbusConnection(string ipAddress, int port)
    {
        _ipAddress = ipAddress ?? throw new ArgumentNullException(nameof(ipAddress));
        _port = port;
        _sendSemaphore = new SemaphoreSlim(MaxConcurrentRequests, MaxConcurrentRequests);
    }
    
    /// <summary>
    /// Connect to the Modbus device
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the connection operation</returns>
    public async Task ConnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ModbusConnection));
        
        lock (_lockObject)
        {
            if (Status == ConnectionStatus.Connected || Status == ConnectionStatus.Connecting)
                return;
            
            Status = ConnectionStatus.Connecting;
        }
        
        try
        {
            _tcpClient = new TcpClient();
            _tcpClient.ReceiveTimeout = (int)ReceiveTimeout.TotalMilliseconds;
            _tcpClient.SendTimeout = (int)ConnectionTimeout.TotalMilliseconds;
            
            using var timeoutCts = new CancellationTokenSource(ConnectionTimeout);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, timeoutCts.Token, _cancellationTokenSource.Token);
            
            await _tcpClient.ConnectAsync(_ipAddress, _port, combinedCts.Token);
            
            _networkStream = _tcpClient.GetStream();
            _lastActivity = DateTime.UtcNow;
            _reconnectAttempts = 0;
            
            // Start receive task
            _receiveTask = Task.Run(() => ReceiveLoop(_cancellationTokenSource.Token), _cancellationTokenSource.Token);
            
            Status = ConnectionStatus.Connected;
        }
        catch (Exception ex)
        {
            Status = ConnectionStatus.Failed;
            CleanupConnection();
            throw new ModbusConnectionException(_ipAddress, _port, "Failed to connect", ex);
        }
    }
    
    /// <summary>
    /// Disconnect from the Modbus device
    /// </summary>
    public async Task DisconnectAsync()
    {
        if (_disposed)
            return;

        Status = ConnectionStatus.Disconnected;

        // Cancel all pending requests
        foreach (var kvp in _pendingRequests)
        {
            kvp.Value.TrySetCanceled();
        }
        _pendingRequests.Clear();

        CleanupConnection();

        if (_receiveTask != null)
        {
            try
            {
                await _receiveTask;
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation token is triggered
            }
        }
    }

    /// <summary>
    /// Send a Modbus request and wait for response
    /// </summary>
    /// <param name="request">Modbus request to send</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Modbus response</returns>
    public async Task<ModbusResponse> SendRequestAsync(ModbusRequest request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ModbusConnection));

        if (!IsConnected)
        {
            await TryReconnectAsync(cancellationToken);
        }

        if (!IsConnected)
            throw new ModbusConnectionException(_ipAddress, _port, "Not connected to device");

        await _sendSemaphore.WaitAsync(cancellationToken);

        try
        {
            var tcs = new TaskCompletionSource<ModbusResponse>();
            _pendingRequests[request.TransactionId] = tcs;

            var requestData = request.ToByteArray();

            using var timeoutCts = new CancellationTokenSource(request.Timeout);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, timeoutCts.Token, _cancellationTokenSource.Token);

            // Register timeout handling
            _ = Task.Delay(request.Timeout, combinedCts.Token).ContinueWith(t =>
            {
                if (!t.IsCanceled && _pendingRequests.TryRemove(request.TransactionId, out var timeoutTcs))
                {
                    timeoutTcs.TrySetException(new ModbusTimeoutException(request.Timeout, request.TransactionId));
                }
            }, TaskScheduler.Default);

            await _networkStream!.WriteAsync(requestData, combinedCts.Token);
            _lastActivity = DateTime.UtcNow;

            return await tcs.Task;
        }
        catch (Exception ex) when (!(ex is ModbusException))
        {
            _pendingRequests.TryRemove(request.TransactionId, out _);

            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = _ipAddress,
                Port = _port,
                ErrorType = CommunicationErrorType.SendFailed,
                ErrorMessage = ex.Message,
                Exception = ex,
                Request = request
            };

            CommunicationError?.Invoke(this, errorArgs);

            // Try to reconnect on communication error
            _ = Task.Run(() => TryReconnectAsync(CancellationToken.None));

            throw new ModbusConnectionException(_ipAddress, _port, "Failed to send request", ex);
        }
        finally
        {
            _sendSemaphore.Release();
        }
    }

    /// <summary>
    /// Attempt to reconnect to the device
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the reconnection attempt</returns>
    private async Task TryReconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed || Status == ConnectionStatus.Connected || Status == ConnectionStatus.Connecting)
            return;

        if (_reconnectAttempts >= MaxReconnectAttempts)
        {
            Status = ConnectionStatus.Failed;
            return;
        }

        Status = ConnectionStatus.Reconnecting;
        _reconnectAttempts++;

        try
        {
            await Task.Delay(ReconnectDelay, cancellationToken);
            await ConnectAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = _ipAddress,
                Port = _port,
                ErrorType = CommunicationErrorType.ConnectionLost,
                ErrorMessage = $"Reconnection attempt {_reconnectAttempts} failed: {ex.Message}",
                Exception = ex
            };

            CommunicationError?.Invoke(this, errorArgs);

            if (_reconnectAttempts >= MaxReconnectAttempts)
            {
                Status = ConnectionStatus.Failed;
            }
        }
    }

    /// <summary>
    /// Receive loop for processing incoming responses
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the receive loop</returns>
    private async Task ReceiveLoop(CancellationToken cancellationToken)
    {
        var buffer = new byte[2048]; // Increased buffer size for larger responses

        try
        {
            while (!cancellationToken.IsCancellationRequested && IsConnected)
            {
                try
                {
                    // Read MBAP header first (6 bytes)
                    var headerBytes = new byte[6];
                    var bytesRead = 0;

                    while (bytesRead < 6 && !cancellationToken.IsCancellationRequested)
                    {
                        var read = await _networkStream!.ReadAsync(
                            headerBytes.AsMemory(bytesRead, 6 - bytesRead),
                            cancellationToken);

                        if (read == 0)
                        {
                            // Connection closed
                            throw new EndOfStreamException("Connection closed by remote host");
                        }

                        bytesRead += read;
                    }

                    if (cancellationToken.IsCancellationRequested)
                        break;

                    // Parse length from header
                    var length = (ushort)((headerBytes[4] << 8) | headerBytes[5]);

                    // Validate length to prevent buffer overflow
                    if (length < 2 || length > 253) // Modbus TCP length should be between 2 and 253
                    {
                        throw new InvalidDataException($"Invalid Modbus TCP length: {length}");
                    }

                    // Read the remaining data
                    var totalLength = 6 + length - 1; // -1 because length includes unit identifier

                    // Ensure totalLength is at least 6 (header size)
                    if (totalLength < 6)
                    {
                        throw new InvalidDataException($"Invalid total length: {totalLength}");
                    }

                    var responseBuffer = new byte[totalLength];
                    Array.Copy(headerBytes, 0, responseBuffer, 0, 6);

                    bytesRead = 6;
                    while (bytesRead < totalLength && !cancellationToken.IsCancellationRequested)
                    {
                        var read = await _networkStream!.ReadAsync(
                            responseBuffer.AsMemory(bytesRead, totalLength - bytesRead),
                            cancellationToken);

                        if (read == 0)
                        {
                            throw new EndOfStreamException("Connection closed by remote host");
                        }

                        bytesRead += read;
                    }

                    if (cancellationToken.IsCancellationRequested)
                        break;

                    _lastActivity = DateTime.UtcNow;

                    // Parse and handle response
                    await HandleResponse(responseBuffer);
                }
                catch (EndOfStreamException)
                {
                    // Connection lost
                    break;
                }
                catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
                {
                    var errorArgs = new CommunicationErrorEventArgs
                    {
                        IpAddress = _ipAddress,
                        Port = _port,
                        ErrorType = CommunicationErrorType.ReceiveFailed,
                        ErrorMessage = $"Receive failed: {ex.Message} (Type: {ex.GetType().Name})",
                        Exception = ex
                    };

                    CommunicationError?.Invoke(this, errorArgs);

                    // Add a small delay before continuing to prevent tight error loops
                    await Task.Delay(100, cancellationToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }
        finally
        {
            if (!cancellationToken.IsCancellationRequested)
            {
                // Connection lost unexpectedly, try to reconnect
                _ = Task.Run(() => TryReconnectAsync(CancellationToken.None));
            }
        }
    }

    /// <summary>
    /// Handle incoming response
    /// </summary>
    /// <param name="responseData">Response data</param>
    /// <returns>Task representing the response handling</returns>
    private Task HandleResponse(byte[] responseData)
    {
        try
        {
            var response = ModbusResponse.Parse(responseData);

            if (_pendingRequests.TryRemove(response.TransactionId, out var tcs))
            {
                if (response.IsException)
                {
                    var exception = new ModbusSlaveException(
                        response.FunctionCode,
                        response.ExceptionCode ?? ModbusExceptionCode.SlaveDeviceFailure,
                        response.UnitId);

                    tcs.SetException(exception);
                }
                else
                {
                    tcs.SetResult(response);
                }
            }
            else
            {
                // Unexpected response
                var errorArgs = new CommunicationErrorEventArgs
                {
                    IpAddress = _ipAddress,
                    Port = _port,
                    ErrorType = CommunicationErrorType.UnexpectedResponse,
                    ErrorMessage = $"Received unexpected response with transaction ID {response.TransactionId}",
                    Response = response
                };

                CommunicationError?.Invoke(this, errorArgs);
            }
        }
        catch (Exception ex)
        {
            var errorArgs = new CommunicationErrorEventArgs
            {
                IpAddress = _ipAddress,
                Port = _port,
                ErrorType = CommunicationErrorType.InvalidResponse,
                ErrorMessage = ex.Message,
                Exception = ex
            };

            CommunicationError?.Invoke(this, errorArgs);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Clean up connection resources
    /// </summary>
    private void CleanupConnection()
    {
        try
        {
            _networkStream?.Close();
            _tcpClient?.Close();
        }
        catch
        {
            // Ignore cleanup errors
        }
        finally
        {
            _networkStream?.Dispose();
            _tcpClient?.Dispose();
            _networkStream = null;
            _tcpClient = null;
        }
    }

    /// <summary>
    /// Dispose of the connection
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        _cancellationTokenSource.Cancel();

        // Cancel all pending requests
        foreach (var kvp in _pendingRequests)
        {
            kvp.Value.TrySetCanceled();
        }
        _pendingRequests.Clear();

        CleanupConnection();

        _cancellationTokenSource.Dispose();
        _sendSemaphore.Dispose();

        GC.SuppressFinalize(this);
    }
}
