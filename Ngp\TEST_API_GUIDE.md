# Modbus TCP Master 測試 API 指南

本指南說明如何使用 Ngp 專案中的 Modbus TCP Master 測試 API 來測試連線到 ***************:502 的功能。

## 🚀 快速開始

### 1. 啟動應用程式

```bash
cd Ngp
dotnet run
```

應用程式將在 `http://localhost:5135` 啟動。

### 2. 預配置的測試裝置

應用程式已預先配置了連線到 `***************:502` 的測試裝置，包含：

- **實例識別**: "TestDevice" (Test Device Instance)
- **協定模式**: Modbus TCP
- **連線數量**: 2 個連線（提高並發性能）
- **動態Write插入**: 已啟用
- **預配置輪詢範圍**:
  - Holding Registers: Unit 1, Address 0-49 (每2秒輪詢)
  - Input Registers: Unit 1, Address 100-119 (每1秒輪詢)
  - Coils: Unit 1, Address 0-31 (每500毫秒輪詢)

## 📋 API 端點總覽

### 🔧 測試裝置專用 API (`/test-device`)

| 方法 | 端點 | 說明 |
|------|------|------|
| GET | `/test-device/status` | 檢查測試裝置連線狀態 |
| GET | `/test-device/quick-read` | 快速讀取預配置地址 |
| GET | `/test-device/polling-status` | 取得輪詢配置和佇列狀態 |
| POST | `/test-device/dynamic-write` | 動態插入Write指令 |
| POST | `/test-device/batch-write` | 批次動態Write指令 |

### 📊 監控 API (`/monitoring`)

| 方法 | 端點 | 說明 |
|------|------|------|
| GET | `/monitoring/system` | 系統整體監控資訊 |
| GET | `/monitoring/test-device` | 測試裝置專用監控 |
| GET | `/monitoring/metrics?minutes=5` | 性能指標（指定時間範圍） |

### ⚙️ 管理 API (`/management`)

| 方法 | 端點 | 說明 |
|------|------|------|
| POST | `/management/add-polling` | 新增輪詢範圍 |
| POST | `/management/remove-polling` | 移除輪詢範圍 |
| POST | `/management/clear-queue` | 清空請求佇列 |

### 🔌 通用 Modbus API (`/modbus`)

| 方法 | 端點 | 說明 |
|------|------|------|
| GET | `/modbus/holding-registers` | 讀取保持暫存器 |
| GET | `/modbus/coils` | 讀取線圈 |
| POST | `/modbus/register` | 寫入單一暫存器 |
| POST | `/modbus/coil` | 寫入單一線圈 |
| GET | `/modbus/connections` | 取得所有連線狀態 |

## 🧪 測試流程建議

### 步驟 1: 檢查連線狀態

```http
GET http://localhost:5135/test-device/status
```

這會顯示：
- 實例資訊
- 連線上下文
- 連線狀態和活動時間

### 步驟 2: 快速讀取測試

```http
GET http://localhost:5135/test-device/quick-read
```

這會讀取：
- Holding Registers 0-9
- Input Registers 100-104
- Coils 0-7

### 步驟 3: 檢查輪詢狀態

```http
GET http://localhost:5135/test-device/polling-status
```

這會顯示：
- 所有輪詢範圍配置
- 佇列大小狀態
- 數據轉換配置

### 步驟 4: 測試動態Write插入

```http
POST http://localhost:5135/test-device/dynamic-write
Content-Type: application/json

{
  "unitId": 1,
  "address": 10,
  "value": 1234,
  "isCoil": false
}
```

這個Write指令會被插入到輪詢過程中，具有高優先級。

### 步驟 5: 測試批次Write

```http
POST http://localhost:5135/test-device/batch-write
Content-Type: application/json

{
  "writes": [
    {
      "unitId": 1,
      "address": 20,
      "value": 2000,
      "isCoil": false
    },
    {
      "unitId": 1,
      "address": 10,
      "value": 1,
      "isCoil": true
    }
  ]
}
```

### 步驟 6: 監控系統狀態

```http
GET http://localhost:5135/monitoring/system
```

```http
GET http://localhost:5135/monitoring/test-device
```

```http
GET http://localhost:5135/monitoring/metrics?minutes=5
```

## 🔍 重要特性展示

### 1. 動態Write插入
- Write指令會被插入到輪詢過程中
- 不會建立新連線
- 具有高優先級，會在當前輪詢完成後立即執行

### 2. 實例管理
- 每個ModbusMaster實例都有唯一識別
- 不以IP:Port作為唯一ID
- 支援多個實例連接同一端點

### 3. 多連線支援
- 單一實例可以有多個連線到同一端點
- 提高並發性能和吞吐量

### 4. 企業級監控
- 即時連線狀態監控
- 性能指標追蹤
- 詳細的錯誤統計

## 📝 使用 Ngp.http 檔案

專案包含完整的 HTTP 測試檔案 `Ngp.http`，可以直接在 Visual Studio 或 VS Code 中使用：

1. 開啟 `Ngp.http` 檔案
2. 確保應用程式正在運行
3. 點擊每個請求旁邊的 "Send Request" 按鈕

## 🛠️ 故障排除

### 連線問題
- 確保 ***************:502 的 Modbus 裝置正在運行
- 檢查網路連線和防火牆設定
- 使用 `/test-device/status` 檢查連線狀態

### 輪詢問題
- 使用 `/test-device/polling-status` 檢查輪詢配置
- 檢查 `/monitoring/test-device` 的錯誤統計
- 使用 `/management/clear-queue` 清空佇列

### 性能問題
- 檢查 `/monitoring/metrics` 的性能指標
- 調整輪詢間隔和連線數量
- 監控佇列大小避免積壓

## 🎯 下一步

1. 根據實際需求調整輪詢配置
2. 測試不同的Write指令組合
3. 監控長時間運行的性能表現
4. 根據監控數據優化配置參數
