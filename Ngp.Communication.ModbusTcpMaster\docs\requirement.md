1. [使用.NET](http://使用.NET) 9開發一個ModbusTCP Master的庫。
2. 符合企業級等級的穩定性及運行性能。
3. 使用Ngp.Communication.ModbusTcpMaster專案。
4. 請使用Minimal API不要用Controller。
5. 請在程式中加入簡易的英文註解。
6. 如有多種作法，請選擇Best Practice。
7. 平行化處理，在實際使用時提供可連線至至少1000台或以上Slave的能力。
8. 以IP跟PORT為一個端點，一個IP跟PORT的組合可能會有多個SLAVE，請使用一個連線就好，暫時不考慮同一個IP跟PORT號。
9. 如果是ModbusTCP的話，請加入「平行化」的設計，即可以同時收發多組訊息加快輪詢速度，此功能請提供開關接口。
10. 支援ModbusTCP及Modbus RTU over TCP的模式，Modbus RTU over TCP的模式，應該被強制設定為一次只能有一組問答。
11. 支援Single Write跟Multiple Write的模式。
12. 要有能夠辨識及處理Slave回傳回來的錯誤碼的能力。
13. 實作所有Modbus協定的指令碼。
14. 符合Modbus的標準規範。
15. 可針對每個IP+PORT的ENDPOINT調整的Timeout及Poll Gap時間。
16. 自己寫引擎，不要使用NModbus或相似的庫。
17. 請使用高性能的TCP管理機制。
18. 確保資源能夠正確回收，正常運作或異常時，連線埠均能正確關閉。
19. 需有一個完善的事件引擎，透過委派的方式，將數值更新、連線異常、CRC錯誤等等的事件，透過平行化的機制傳遞出去。
20. 連線與斷線重試的機制性能要強，同時也要避免用盡Socket的問題。
21. 提供FluentAPI接口。
22. 裝置的TCP連線需可以在外部管理，可以在實際引用這個引擎時，確認TCP連線的連線狀態等等。
23. 功能需要Thread-Safe。
24. 我會需要讀取狀態的暫存器清單，要自動分別轉成相對應的暫存器(如Coil/HoldingRegister等)合理的Modbus Command後輪循，不停取得最新狀態，避免過短或過長的暫存器請求範圍(請注意Modbus的暫存器長度限制)，提高效率，需要有可以設定的機制限制暫存器請求範圍。
25. 應該要可以Graceful Exit。
26. 數值應該可以被拆成各種不同型別，包含不同的Endian的排列組合方式，再傳到委派事件裡面。
27. 不要使用Console.WriteLine，請使用ILogger，並合理配置不同的Log等級。
28. 實現一個狀態監控的接口，可以隨時得知目前連線了多少終端，每個終端配置了多少SLAVE或是多少暫存器要輪詢，每個終端各別的回應速度如何，輪詢次數有多少次了，以及寫入了哪些資料，產生了多少錯誤，等等的功能，請維持本專案為Class Library，不要用RESTFUL API，用其它方式供調用。