namespace Ngp.Communication.ModbusTcpMaster.Monitoring;

using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Engine;

/// <summary>
/// Interface for monitoring ModbusMaster performance and statistics
/// </summary>
public interface IMonitoringService
{
    /// <summary>
    /// Get current system monitoring information
    /// </summary>
    SystemMonitoringInfo GetSystemMonitoringInfo();

    /// <summary>
    /// Get monitoring information for a specific endpoint
    /// </summary>
    EndpointMonitoringInfo GetEndpointMonitoringInfo(string ipAddress, int port);

    /// <summary>
    /// Get performance metrics for a specific time window
    /// </summary>
    PerformanceMetrics GetPerformanceMetrics(TimeSpan timeWindow);

    /// <summary>
    /// Record a write operation
    /// </summary>
    void RecordWriteOperation(WriteOperationInfo writeInfo);

    /// <summary>
    /// Reset statistics for all endpoints
    /// </summary>
    void ResetStatistics();

    /// <summary>
    /// Get all connected endpoints
    /// </summary>
    IEnumerable<string> GetConnectedEndpoints();

    /// <summary>
    /// Get total number of configured slaves across all endpoints
    /// </summary>
    int GetTotalConfiguredSlaves();

    /// <summary>
    /// Get total number of polling ranges across all endpoints
    /// </summary>
    int GetTotalPollingRanges();

    /// <summary>
    /// Get overall system health status
    /// </summary>
    SystemHealthStatus GetSystemHealthStatus();
}

/// <summary>
/// System health status enumeration
/// </summary>
public enum SystemHealthStatus
{
    Healthy,
    Warning,
    Critical,
    Unknown
}

/// <summary>
/// Service for monitoring ModbusMaster performance and statistics
/// </summary>
public class MonitoringService : IMonitoringService, IDisposable
{
    private readonly ILogger<MonitoringService> _logger;
    private readonly ModbusMaster _modbusMaster;
    private readonly ConcurrentDictionary<string, EndpointStatistics> _endpointStats = new();
    private readonly ConcurrentQueue<WriteOperationInfo> _recentWrites = new();
    private readonly Timer _metricsTimer;
    private readonly object _lockObject = new();
    private readonly DateTime _startTime = DateTime.UtcNow;
    private readonly Process _currentProcess = Process.GetCurrentProcess();
    
    private long _totalRequestsSent = 0;
    private long _totalResponsesReceived = 0;
    private long _totalErrors = 0;
    private bool _disposed = false;
    
    /// <summary>
    /// Maximum number of recent write operations to keep
    /// </summary>
    public int MaxRecentWrites { get; set; } = 1000;
    
    /// <summary>
    /// Metrics collection interval
    /// </summary>
    public TimeSpan MetricsInterval { get; set; } = TimeSpan.FromSeconds(10);
    
    public MonitoringService(ModbusMaster modbusMaster, ILogger<MonitoringService> logger)
    {
        _modbusMaster = modbusMaster ?? throw new ArgumentNullException(nameof(modbusMaster));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Subscribe to events
        _modbusMaster.DataValueUpdated += OnDataValueUpdated;
        _modbusMaster.ConnectionStatusChanged += OnConnectionStatusChanged;
        _modbusMaster.CommunicationError += OnCommunicationError;
        _modbusMaster.ModbusExceptionOccurred += OnModbusException;
        _modbusMaster.TimeoutOccurred += OnTimeout;
        
        // Start metrics collection timer
        _metricsTimer = new Timer(CollectMetrics, null, MetricsInterval, MetricsInterval);
        
        _logger.LogInformation("Monitoring service started");
    }
    
    /// <summary>
    /// Get current system monitoring information
    /// </summary>
    /// <returns>System monitoring information</returns>
    public SystemMonitoringInfo GetSystemMonitoringInfo()
    {
        var connections = _modbusMaster.ConnectionManager.GetConnectionInfo().ToArray();
        var systemInfo = new SystemMonitoringInfo
        {
            TotalConnectedEndpoints = connections.Count(c => c.Status == ConnectionStatus.Connected),
            SystemUptime = DateTime.UtcNow - _startTime,
            TotalRequestsSent = _totalRequestsSent,
            TotalResponsesReceived = _totalResponsesReceived,
            TotalErrors = _totalErrors,
            MemoryUsageMB = _currentProcess.WorkingSet64 / (1024.0 * 1024.0),
            Timestamp = DateTime.UtcNow
        };
        
        // Calculate system-wide averages
        var endpointInfos = new List<EndpointMonitoringInfo>();
        var totalSlaves = 0;
        var totalPollingRanges = 0;
        var totalRegisters = 0;
        var totalResponseTime = 0.0;
        var responseTimeCount = 0;
        
        foreach (var connection in connections)
        {
            var key = $"{connection.IpAddress}:{connection.Port}";
            var endpointInfo = GetEndpointMonitoringInfo(connection.IpAddress, connection.Port);
            endpointInfos.Add(endpointInfo);
            
            totalSlaves += endpointInfo.ConfiguredSlaves;
            totalPollingRanges += endpointInfo.PollingRanges;
            totalRegisters += endpointInfo.TotalRegisters;
            
            if (endpointInfo.AverageResponseTimeMs > 0)
            {
                totalResponseTime += endpointInfo.AverageResponseTimeMs;
                responseTimeCount++;
            }
        }
        
        systemInfo.TotalConfiguredSlaves = totalSlaves;
        systemInfo.TotalPollingRanges = totalPollingRanges;
        systemInfo.TotalRegistersPolled = totalRegisters;
        systemInfo.AverageResponseTimeMs = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0;
        systemInfo.Endpoints = endpointInfos;
        
        return systemInfo;
    }
    
    /// <summary>
    /// Get monitoring information for a specific endpoint
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>Endpoint monitoring information</returns>
    public EndpointMonitoringInfo GetEndpointMonitoringInfo(string ipAddress, int port)
    {
        var key = $"{ipAddress}:{port}";
        var stats = _endpointStats.GetOrAdd(key, _ => new EndpointStatistics());
        var connectionInfo = _modbusMaster.ConnectionManager.GetConnectionInfo()
            .FirstOrDefault(c => c.IpAddress == ipAddress && c.Port == port);
        
        var pollingRanges = _modbusMaster.PollingEngine.GetPollingRanges(ipAddress, port).ToArray();
        var slaves = pollingRanges.GroupBy(r => r.UnitId).ToArray();
        
        var endpointInfo = new EndpointMonitoringInfo
        {
            IpAddress = ipAddress,
            Port = port,
            Status = connectionInfo?.Status ?? ConnectionStatus.Disconnected,
            ConfiguredSlaves = slaves.Length,
            PollingRanges = pollingRanges.Length,
            TotalRegisters = pollingRanges.Sum(r => r.Count),
            PendingRequests = connectionInfo?.PendingRequestCount ?? 0,
            RequestsSent = stats.RequestsSent,
            ResponsesReceived = stats.ResponsesReceived,
            TotalErrors = stats.TotalErrors,
            TotalPollingCycles = stats.TotalPollingCycles,
            LastSuccessfulCommunication = stats.LastSuccessfulCommunication,
            LastErrorTime = stats.LastErrorTime,
            LastErrorMessage = stats.LastErrorMessage,
            ReconnectionAttempts = stats.ReconnectionAttempts,
            ErrorBreakdown = new Dictionary<string, long>(stats.ErrorBreakdown)
        };
        
        // Calculate response time statistics
        if (stats.ResponseTimes.Count > 0)
        {
            endpointInfo.AverageResponseTimeMs = stats.ResponseTimes.Average();
            endpointInfo.MinResponseTimeMs = stats.ResponseTimes.Min();
            endpointInfo.MaxResponseTimeMs = stats.ResponseTimes.Max();
        }
        
        // Get recent writes for this endpoint
        endpointInfo.RecentWrites = _recentWrites
            .Where(w => w.Timestamp > DateTime.UtcNow.AddMinutes(-10))
            .Take(50)
            .ToList();
        
        // Get slave-specific information
        endpointInfo.Slaves = slaves.Select(slaveGroup =>
        {
            var unitId = slaveGroup.Key;
            var slaveRanges = slaveGroup.ToArray();
            var slaveStats = stats.SlaveStats.GetOrAdd(unitId, _ => new SlaveStatistics());
            
            return new SlaveMonitoringInfo
            {
                UnitId = unitId,
                PollingRanges = slaveRanges.Length,
                TotalRegisters = slaveRanges.Sum(r => r.Count),
                AverageResponseTimeMs = slaveStats.ResponseTimes.Count > 0 ? slaveStats.ResponseTimes.Average() : 0,
                TotalPollingCycles = slaveStats.TotalPollingCycles,
                TotalErrors = slaveStats.TotalErrors,
                LastSuccessfulCommunication = slaveStats.LastSuccessfulCommunication,
                RegisterTypeBreakdown = slaveRanges
                    .GroupBy(r => r.RegisterType)
                    .ToDictionary(g => g.Key, g => g.Sum(r => r.Count))
            };
        }).ToList();
        
        return endpointInfo;
    }
    
    /// <summary>
    /// Get performance metrics for a specific time window
    /// </summary>
    /// <param name="timeWindow">Time window for metrics</param>
    /// <returns>Performance metrics</returns>
    public PerformanceMetrics GetPerformanceMetrics(TimeSpan timeWindow)
    {
        var cutoffTime = DateTime.UtcNow - timeWindow;
        var totalRequests = 0L;
        var totalResponses = 0L;
        var totalErrors = 0L;
        
        foreach (var stats in _endpointStats.Values)
        {
            totalRequests += stats.RequestsSent;
            totalResponses += stats.ResponsesReceived;
            totalErrors += stats.TotalErrors;
        }
        
        var timeWindowSeconds = timeWindow.TotalSeconds;
        
        return new PerformanceMetrics
        {
            RequestsPerSecond = totalRequests / timeWindowSeconds,
            ResponsesPerSecond = totalResponses / timeWindowSeconds,
            ErrorsPerSecond = totalErrors / timeWindowSeconds,
            SuccessRatePercent = totalRequests > 0 ? (double)totalResponses / totalRequests * 100 : 0,
            TimeWindow = timeWindow,
            Timestamp = DateTime.UtcNow
        };
    }
    
    /// <summary>
    /// Record a write operation
    /// </summary>
    /// <param name="writeInfo">Write operation information</param>
    public void RecordWriteOperation(WriteOperationInfo writeInfo)
    {
        _recentWrites.Enqueue(writeInfo);
        
        // Keep only recent writes
        while (_recentWrites.Count > MaxRecentWrites)
        {
            _recentWrites.TryDequeue(out _);
        }
        
        _logger.LogDebug("Recorded write operation: {FunctionCode} to {UnitId}:{StartAddress} ({Success})",
            writeInfo.FunctionCode, writeInfo.UnitId, writeInfo.StartAddress, writeInfo.Success);
    }
    
    /// <summary>
    /// Reset statistics for all endpoints
    /// </summary>
    public void ResetStatistics()
    {
        lock (_lockObject)
        {
            _endpointStats.Clear();
            while (_recentWrites.TryDequeue(out _)) { }
            _totalRequestsSent = 0;
            _totalResponsesReceived = 0;
            _totalErrors = 0;
        }

        _logger.LogInformation("Monitoring statistics reset");
    }

    /// <summary>
    /// Get all connected endpoints
    /// </summary>
    /// <returns>List of connected endpoint identifiers</returns>
    public IEnumerable<string> GetConnectedEndpoints()
    {
        return _modbusMaster.ConnectionManager.GetConnectionInfo()
            .Where(c => c.Status == ConnectionStatus.Connected)
            .Select(c => $"{c.IpAddress}:{c.Port}");
    }

    /// <summary>
    /// Get total number of configured slaves across all endpoints
    /// </summary>
    /// <returns>Total number of configured slaves</returns>
    public int GetTotalConfiguredSlaves()
    {
        var connections = _modbusMaster.ConnectionManager.GetConnectionInfo();
        var totalSlaves = 0;

        foreach (var connection in connections)
        {
            var pollingRanges = _modbusMaster.PollingEngine.GetPollingRanges(connection.IpAddress, connection.Port);
            var slaves = pollingRanges.GroupBy(r => r.UnitId).Count();
            totalSlaves += slaves;
        }

        return totalSlaves;
    }

    /// <summary>
    /// Get total number of polling ranges across all endpoints
    /// </summary>
    /// <returns>Total number of polling ranges</returns>
    public int GetTotalPollingRanges()
    {
        var connections = _modbusMaster.ConnectionManager.GetConnectionInfo();
        var totalRanges = 0;

        foreach (var connection in connections)
        {
            var pollingRanges = _modbusMaster.PollingEngine.GetPollingRanges(connection.IpAddress, connection.Port);
            totalRanges += pollingRanges.Count();
        }

        return totalRanges;
    }

    /// <summary>
    /// Get overall system health status
    /// </summary>
    /// <returns>System health status</returns>
    public SystemHealthStatus GetSystemHealthStatus()
    {
        var systemInfo = GetSystemMonitoringInfo();

        // Calculate error rate
        var totalRequests = systemInfo.TotalRequestsSent;
        var totalErrors = systemInfo.TotalErrors;
        var errorRate = totalRequests > 0 ? (double)totalErrors / totalRequests : 0;

        // Check memory usage (consider critical if over 500MB)
        var memoryUsage = systemInfo.MemoryUsageMB;

        // Check if any endpoints are disconnected
        var hasDisconnectedEndpoints = systemInfo.Endpoints.Any(e => e.Status != ConnectionStatus.Connected);

        // Determine health status
        if (errorRate > 0.1 || memoryUsage > 500 || totalErrors > 1000)
        {
            return SystemHealthStatus.Critical;
        }
        else if (errorRate > 0.05 || memoryUsage > 200 || hasDisconnectedEndpoints || totalErrors > 100)
        {
            return SystemHealthStatus.Warning;
        }
        else if (systemInfo.TotalConnectedEndpoints > 0)
        {
            return SystemHealthStatus.Healthy;
        }
        else
        {
            return SystemHealthStatus.Unknown;
        }
    }

    /// <summary>
    /// Get quick system summary
    /// </summary>
    /// <returns>Quick system summary</returns>
    public string GetQuickSystemSummary()
    {
        var systemInfo = GetSystemMonitoringInfo();
        var healthStatus = GetSystemHealthStatus();

        return $"Status: {healthStatus} | " +
               $"Endpoints: {systemInfo.TotalConnectedEndpoints} | " +
               $"Slaves: {systemInfo.TotalConfiguredSlaves} | " +
               $"Requests: {systemInfo.TotalRequestsSent} | " +
               $"Errors: {systemInfo.TotalErrors} | " +
               $"Memory: {systemInfo.MemoryUsageMB:F1}MB | " +
               $"Uptime: {systemInfo.SystemUptime:hh\\:mm\\:ss}";
    }

    /// <summary>
    /// Get endpoint summary for a specific endpoint
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>Endpoint summary</returns>
    public string GetEndpointSummary(string ipAddress, int port)
    {
        var endpointInfo = GetEndpointMonitoringInfo(ipAddress, port);

        return $"Endpoint {ipAddress}:{port} | " +
               $"Status: {endpointInfo.Status} | " +
               $"Slaves: {endpointInfo.ConfiguredSlaves} | " +
               $"Ranges: {endpointInfo.PollingRanges} | " +
               $"Registers: {endpointInfo.TotalRegisters} | " +
               $"Avg Response: {endpointInfo.AverageResponseTimeMs:F2}ms | " +
               $"Errors: {endpointInfo.TotalErrors}";
    }
    
    private void OnDataValueUpdated(object? sender, Events.DataValueUpdatedEventArgs e)
    {
        var key = $"{e.IpAddress}:{e.Port}";
        var stats = _endpointStats.GetOrAdd(key, _ => new EndpointStatistics());
        
        Interlocked.Increment(ref _totalResponsesReceived);
        Interlocked.Increment(ref stats.ResponsesReceived);
        Interlocked.Increment(ref stats.TotalPollingCycles);
        
        stats.LastSuccessfulCommunication = DateTime.UtcNow;
        
        var slaveStats = stats.SlaveStats.GetOrAdd(e.UnitId, _ => new SlaveStatistics());
        Interlocked.Increment(ref slaveStats.TotalPollingCycles);
        slaveStats.LastSuccessfulCommunication = DateTime.UtcNow;
        
        _logger.LogTrace("Data value updated for {IpAddress}:{Port} Unit:{UnitId} {RegisterType}",
            e.IpAddress, e.Port, e.UnitId, e.RegisterType);
    }

    private void OnConnectionStatusChanged(object? sender, Events.ConnectionStatusChangedEventArgs e)
    {
        var key = $"{e.IpAddress}:{e.Port}";
        var stats = _endpointStats.GetOrAdd(key, _ => new EndpointStatistics());

        if (e.CurrentStatus == ConnectionStatus.Connected)
        {
            stats.ConnectionStartTime = DateTime.UtcNow;
            _logger.LogInformation("Connection established to {IpAddress}:{Port}", e.IpAddress, e.Port);
        }
        else if (e.PreviousStatus == ConnectionStatus.Connected)
        {
            _logger.LogWarning("Connection lost to {IpAddress}:{Port} - {ErrorMessage}",
                e.IpAddress, e.Port, e.ErrorMessage);
        }

        if (e.CurrentStatus == ConnectionStatus.Reconnecting)
        {
            Interlocked.Increment(ref stats.ReconnectionAttempts);
        }
    }

    private void OnCommunicationError(object? sender, Events.CommunicationErrorEventArgs e)
    {
        var key = $"{e.IpAddress}:{e.Port}";
        var stats = _endpointStats.GetOrAdd(key, _ => new EndpointStatistics());

        Interlocked.Increment(ref _totalErrors);
        Interlocked.Increment(ref stats.TotalErrors);

        stats.LastErrorTime = DateTime.UtcNow;
        stats.LastErrorMessage = e.ErrorMessage;

        var errorType = e.ErrorType.ToString();
        stats.ErrorBreakdown.AddOrUpdate(errorType, 1, (key, value) => value + 1);

        if (e.UnitId > 0)
        {
            var slaveStats = stats.SlaveStats.GetOrAdd(e.UnitId, _ => new SlaveStatistics());
            Interlocked.Increment(ref slaveStats.TotalErrors);
        }

        _logger.LogError("Communication error for {IpAddress}:{Port} - {ErrorType}: {ErrorMessage}",
            e.IpAddress, e.Port, e.ErrorType, e.ErrorMessage);
    }

    private void OnModbusException(object? sender, Events.ModbusExceptionEventArgs e)
    {
        var key = $"{e.IpAddress}:{e.Port}";
        var stats = _endpointStats.GetOrAdd(key, _ => new EndpointStatistics());

        Interlocked.Increment(ref _totalErrors);
        Interlocked.Increment(ref stats.TotalErrors);

        stats.LastErrorTime = DateTime.UtcNow;
        stats.LastErrorMessage = $"Modbus Exception: {e.ExceptionCode}";

        var errorType = $"ModbusException_{e.ExceptionCode}";
        stats.ErrorBreakdown.AddOrUpdate(errorType, 1, (key, value) => value + 1);

        var slaveStats = stats.SlaveStats.GetOrAdd(e.UnitId, _ => new SlaveStatistics());
        Interlocked.Increment(ref slaveStats.TotalErrors);

        _logger.LogWarning("Modbus exception for {IpAddress}:{Port} Unit:{UnitId} - {FunctionCode}: {ExceptionCode}",
            e.IpAddress, e.Port, e.UnitId, e.FunctionCode, e.ExceptionCode);
    }

    private void OnTimeout(object? sender, Events.TimeoutEventArgs e)
    {
        var key = $"{e.IpAddress}:{e.Port}";
        var stats = _endpointStats.GetOrAdd(key, _ => new EndpointStatistics());

        Interlocked.Increment(ref _totalErrors);
        Interlocked.Increment(ref stats.TotalErrors);

        stats.LastErrorTime = DateTime.UtcNow;
        stats.LastErrorMessage = $"Timeout after {e.Timeout.TotalMilliseconds}ms";

        stats.ErrorBreakdown.AddOrUpdate("Timeout", 1, (key, value) => value + 1);

        var slaveStats = stats.SlaveStats.GetOrAdd(e.UnitId, _ => new SlaveStatistics());
        Interlocked.Increment(ref slaveStats.TotalErrors);

        _logger.LogWarning("Timeout for {IpAddress}:{Port} Unit:{UnitId} after {Timeout}ms",
            e.IpAddress, e.Port, e.UnitId, e.Timeout.TotalMilliseconds);
    }

    private void CollectMetrics(object? state)
    {
        try
        {
            // Update memory usage
            _currentProcess.Refresh();

            // Log periodic metrics
            var systemInfo = GetSystemMonitoringInfo();
            _logger.LogInformation("System metrics - Endpoints: {Endpoints}, Requests: {Requests}, Errors: {Errors}, Memory: {Memory:F1}MB",
                systemInfo.TotalConnectedEndpoints, systemInfo.TotalRequestsSent, systemInfo.TotalErrors, systemInfo.MemoryUsageMB);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting metrics");
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        _metricsTimer?.Dispose();
        _currentProcess?.Dispose();

        _logger.LogInformation("Monitoring service disposed");

        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Internal statistics for an endpoint
/// </summary>
internal class EndpointStatistics
{
    public long RequestsSent = 0;
    public long ResponsesReceived = 0;
    public long TotalErrors = 0;
    public long TotalPollingCycles = 0;
    public int ReconnectionAttempts = 0;
    public DateTime? LastSuccessfulCommunication;
    public DateTime? LastErrorTime;
    public string? LastErrorMessage;
    public DateTime? ConnectionStartTime;
    public readonly List<double> ResponseTimes = new();
    public readonly ConcurrentDictionary<string, long> ErrorBreakdown = new();
    public readonly ConcurrentDictionary<byte, SlaveStatistics> SlaveStats = new();
}

/// <summary>
/// Internal statistics for a slave
/// </summary>
internal class SlaveStatistics
{
    public long TotalPollingCycles = 0;
    public long TotalErrors = 0;
    public DateTime? LastSuccessfulCommunication;
    public readonly List<double> ResponseTimes = new();
}
