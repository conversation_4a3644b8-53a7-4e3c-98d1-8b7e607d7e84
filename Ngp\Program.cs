using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Fluent;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Conversion;
using Ngp.Communication.ModbusTcpMaster.Monitoring;

var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Add Swagger services
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "Modbus TCP Master API",
        Version = "v1",
        Description = "Enterprise-grade Modbus TCP Master API for industrial automation",
        Contact = new Microsoft.OpenApi.Models.OpenApiContact
        {
            Name = "Modbus TCP Master",
            Email = "<EMAIL>"
        }
    });

    // Add XML comments if available
    options.EnableAnnotations();

    // Group endpoints by tags
    options.TagActionsBy(api => new[] { api.GroupName ?? "Default" });
    options.DocInclusionPredicate((name, api) => true);
});

// Register ModbusMaster as singleton with test device configuration
builder.Services.AddSingleton<ModbusMaster>(serviceProvider =>
{
    var logger = serviceProvider.GetRequiredService<ILogger<ModbusMaster>>();

    // Create instance for test device
    var testInstance = ModbusMasterInstance.Create("TestDevice", "Test Device Instance");

    var modbusMaster = new ModbusMasterBuilder(logger, testInstance)
        .WithProtocolMode(ModbusProtocolMode.ModbusTcp)
        .WithDefaultTimeout(TimeSpan.FromSeconds(5))
        .WithGapTime(TimeSpan.FromMilliseconds(10))
        .WithParallelProcessing(true)
        .WithMaxParallelRequests(100)
        .WithConnectionSettings(
            connectionTimeout: TimeSpan.FromSeconds(5),
            receiveTimeout: TimeSpan.FromSeconds(5),
            maxConcurrentRequestsPerConnection: 10,
            maxReconnectAttempts: 3,
            reconnectDelay: TimeSpan.FromSeconds(1))
        .WithPollingSettings(
            pollingCheckInterval: TimeSpan.FromMilliseconds(100),
            maxConcurrentPolling: 50,
            enableChangeDetection: true,
            maxRegisterRangeSize: 100,
            maxCoilRangeSize: 1000)
        // Pre-configure test device at ***************:502
        .AddDevice("***************", 502)
            .WithTimeout(TimeSpan.FromSeconds(3))
            .WithGapTime(TimeSpan.FromMilliseconds(50))
            .WithDynamicWriteInsertion(true)
            .WithMultipleConnections(2) // Use 2 connections for better performance
            .WithParallelProcessing(true, maxParallelRequests: 20)
            // Pre-configure some polling ranges for testing
            .PollHoldingRegisters(unitId: 1, startAddress: 0, count: 50,
                pollingInterval: TimeSpan.FromSeconds(2))
                .WithDataConversion(0, ModbusDataType.Int16, EndianType.BigEndian, name: "Temperature")
                .WithDataConversion(1, ModbusDataType.UInt16, EndianType.BigEndian, name: "Pressure")
                .WithDataConversion(2, ModbusDataType.Float, EndianType.BigEndian, name: "FlowRate")
            .PollInputRegisters(unitId: 1, startAddress: 100, count: 20,
                pollingInterval: TimeSpan.FromSeconds(1))
            .PollCoils(unitId: 1, startAddress: 0, count: 32,
                pollingInterval: TimeSpan.FromMilliseconds(500))
        .EndDevice()
        .OnDataValueUpdated((sender, e) =>
        {
            logger.LogInformation("Data updated: {IpAddress}:{Port} Unit:{UnitId} {RegisterType} Address:{StartAddress}",
                e.IpAddress, e.Port, e.UnitId, e.RegisterType, e.StartAddress);

            // Log converted values if available
            foreach (var converted in e.ConvertedValues)
            {
                logger.LogDebug("Converted: {Name} = {Value} ({DataType}, {EndianType})",
                    converted.Config.Name, converted.Value, converted.Config.DataType, converted.Config.EndianType);
            }
        })
        .OnConnectionStatusChanged((sender, e) =>
        {
            logger.LogInformation("Connection status changed: {IpAddress}:{Port} {PreviousStatus} -> {CurrentStatus}",
                e.IpAddress, e.Port, e.PreviousStatus, e.CurrentStatus);
        })
        .OnCommunicationError((sender, e) =>
        {
            logger.LogWarning("Communication error: {IpAddress}:{Port} {ErrorType} - {ErrorMessage}",
                e.IpAddress, e.Port, e.ErrorType, e.ErrorMessage);
        })
        .Build();

    return modbusMaster;
});

// Register MonitoringService as singleton
builder.Services.AddSingleton<MonitoringService>(serviceProvider =>
{
    var modbusMaster = serviceProvider.GetRequiredService<ModbusMaster>();
    var logger = serviceProvider.GetRequiredService<ILogger<MonitoringService>>();
    return new MonitoringService(modbusMaster, logger);
});

// Register IMonitoringService interface
builder.Services.AddSingleton<IMonitoringService>(serviceProvider =>
    serviceProvider.GetRequiredService<MonitoringService>());

var app = builder.Build();

app.MapDefaultEndpoints();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();

    // Enable Swagger UI
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        options.SwaggerEndpoint("/swagger/v1/swagger.json", "Modbus TCP Master API v1");
        options.RoutePrefix = "swagger"; // Swagger UI will be available at /swagger
        options.DocumentTitle = "Modbus TCP Master API Documentation";
        options.DefaultModelsExpandDepth(-1); // Hide models section by default
        options.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.List);
        options.EnableDeepLinking();
        options.EnableFilter();
        options.ShowExtensions();
        options.EnableValidator();

        // Custom CSS for better appearance
        options.InjectStylesheet("/swagger-ui/custom.css");
    });
}

app.UseHttpsRedirection();

// Enable static files for custom CSS
app.UseStaticFiles();

// Start ModbusMaster and MonitoringService on application startup
var modbusMaster = app.Services.GetRequiredService<ModbusMaster>();
var monitoringService = app.Services.GetRequiredService<MonitoringService>();
await modbusMaster.StartAsync();

// Graceful shutdown
app.Lifetime.ApplicationStopping.Register(async () =>
{
    await modbusMaster.StopAsync();
    modbusMaster.Dispose();
    monitoringService.Dispose();
});

// Test Device API endpoints (specifically for ***************:502)
var testDeviceApi = app.MapGroup("/test-device").WithTags("Test Device (***************:502)");

// Quick test connection
testDeviceApi.MapGet("/status", async () =>
{
    try
    {
        var connections = modbusMaster.GetConnectionContexts("***************", 502);
        var connectionInfo = modbusMaster.ConnectionManager.GetConnectionInfo()
            .Where(c => c.IpAddress == "***************" && c.Port == 502)
            .ToList();

        return Results.Ok(new {
            success = true,
            instance = modbusMaster.Instance.ToString(),
            connections = connections.Select(c => c.ToString()),
            connectionStatus = connectionInfo.Select(c => new {
                endpoint = $"{c.IpAddress}:{c.Port}",
                status = c.Status.ToString(),
                lastActivity = c.LastActivity,
                pendingRequestCount = c.PendingRequestCount
            })
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("TestDeviceStatus")
.WithSummary("Get status of test device connections")
.WithDescription("Returns connection status, instance information, and activity details for the pre-configured test device at ***************:502")
.Produces<object>(200, "application/json")
.Produces<object>(400, "application/json");

// Quick read test (reads pre-configured polling data)
testDeviceApi.MapGet("/quick-read", async () =>
{
    try
    {
        var holdingRegs = await modbusMaster.ReadHoldingRegistersAsync("***************", 502, 1, 0, 10);
        var inputRegs = await modbusMaster.ReadInputRegistersAsync("***************", 502, 1, 100, 5);
        var coils = await modbusMaster.ReadCoilsAsync("***************", 502, 1, 0, 8);

        return Results.Ok(new {
            success = true,
            timestamp = DateTime.UtcNow,
            data = new {
                holdingRegisters = holdingRegs.Select((value, index) => new { address = index, value }),
                inputRegisters = inputRegs.Select((value, index) => new { address = 100 + index, value }),
                coils = coils.Select((value, index) => new { address = index, value })
            }
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("QuickRead")
.WithSummary("Quick read test from pre-configured addresses")
.WithDescription("Performs a quick read test from the test device, reading Holding Registers (0-9), Input Registers (100-104), and Coils (0-7)")
.Produces<object>(200, "application/json")
.Produces<object>(400, "application/json");

// Dynamic write with polling insertion
testDeviceApi.MapPost("/dynamic-write", async (DynamicWriteRequest request) =>
{
    try
    {
        if (request.IsCoil)
        {
            await modbusMaster.QueueWriteRequestAsync("***************", 502, request.UnitId,
                request.Address, request.Value, true);
        }
        else
        {
            await modbusMaster.QueueWriteRequestAsync("***************", 502, request.UnitId,
                request.Address, request.Value);
        }

        return Results.Ok(new {
            success = true,
            message = $"Dynamic write queued: {(request.IsCoil ? "Coil" : "Register")} {request.Address} = {request.Value}",
            timestamp = DateTime.UtcNow
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("DynamicWrite")
.WithSummary("Queue a write request that will be inserted into polling")
.WithDescription("Queues a high-priority write request that will be inserted into the polling cycle without creating new connections. Supports both registers and coils.")
.Accepts<DynamicWriteRequest>("application/json")
.Produces<object>(200, "application/json")
.Produces<object>(400, "application/json");

// Batch dynamic writes
testDeviceApi.MapPost("/batch-write", async (BatchWriteRequest request) =>
{
    try
    {
        var tasks = new List<Task>();
        var results = new List<object>();

        foreach (var write in request.Writes)
        {
            if (write.IsCoil)
            {
                tasks.Add(modbusMaster.QueueWriteRequestAsync("***************", 502, write.UnitId,
                    write.Address, write.Value, true));
            }
            else
            {
                tasks.Add(modbusMaster.QueueWriteRequestAsync("***************", 502, write.UnitId,
                    write.Address, write.Value));
            }

            results.Add(new {
                type = write.IsCoil ? "Coil" : "Register",
                address = write.Address,
                value = write.Value,
                unitId = write.UnitId
            });
        }

        await Task.WhenAll(tasks);

        return Results.Ok(new {
            success = true,
            message = $"Batch write queued: {request.Writes.Length} operations",
            operations = results,
            timestamp = DateTime.UtcNow
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("BatchWrite")
.WithSummary("Queue multiple write requests for insertion into polling");

// Get polling status
testDeviceApi.MapGet("/polling-status", () =>
{
    try
    {
        var ranges = modbusMaster.PollingEngine.GetPollingRanges("***************", 502);
        var queueSizes = modbusMaster.RequestQueueManager?.GetAllQueueSizes() ?? new Dictionary<string, int>();

        return Results.Ok(new {
            success = true,
            pollingRanges = ranges.Select(r => new {
                registerType = r.RegisterType.ToString(),
                unitId = r.UnitId,
                startAddress = r.StartAddress,
                count = r.Count,
                pollingInterval = r.PollingInterval.TotalMilliseconds,
                dataConversions = r.DataConversions.Select(dc => new {
                    name = dc.Name,
                    dataType = dc.DataType.ToString(),
                    startAddress = dc.StartAddress,
                    registerCount = dc.RegisterCount
                })
            }),
            queueSizes = queueSizes
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("PollingStatus")
.WithSummary("Get current polling configuration and queue status");

// Modbus TCP Master API endpoints
var modbusApi = app.MapGroup("/modbus").WithTags("Modbus TCP Master");

// Read coils
modbusApi.MapGet("/coils", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadCoilsAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadCoils")
.WithSummary("Read coils from a Modbus device");

// Read discrete inputs
modbusApi.MapGet("/discrete-inputs", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadDiscreteInputsAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadDiscreteInputs")
.WithSummary("Read discrete inputs from a Modbus device");

// Read holding registers
modbusApi.MapGet("/holding-registers", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadHoldingRegistersAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadHoldingRegisters")
.WithSummary("Read holding registers from a Modbus device");

// Read input registers
modbusApi.MapGet("/input-registers", async (string ip, int port, byte unitId, ushort startAddress, ushort quantity) =>
{
    try
    {
        var values = await modbusMaster.ReadInputRegistersAsync(ip, port, unitId, startAddress, quantity);
        return Results.Ok(new { success = true, values });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ReadInputRegisters")
.WithSummary("Read input registers from a Modbus device");

// Write single coil
modbusApi.MapPost("/coil", async (WriteCoilRequest request) =>
{
    try
    {
        await modbusMaster.WriteSingleCoilAsync(request.Ip, request.Port, request.UnitId, request.Address, request.Value);
        return Results.Ok(new { success = true, message = "Coil written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteSingleCoil")
.WithSummary("Write a single coil to a Modbus device");

// Write single register
modbusApi.MapPost("/register", async (WriteRegisterRequest request) =>
{
    try
    {
        await modbusMaster.WriteSingleRegisterAsync(request.Ip, request.Port, request.UnitId, request.Address, request.Value);
        return Results.Ok(new { success = true, message = "Register written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteSingleRegister")
.WithSummary("Write a single register to a Modbus device");

// Write multiple coils
modbusApi.MapPost("/coils", async (WriteMultipleCoilsRequest request) =>
{
    try
    {
        await modbusMaster.WriteMultipleCoilsAsync(request.Ip, request.Port, request.UnitId, request.StartAddress, request.Values);
        return Results.Ok(new { success = true, message = "Coils written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteMultipleCoils")
.WithSummary("Write multiple coils to a Modbus device");

// Write multiple registers
modbusApi.MapPost("/registers", async (WriteMultipleRegistersRequest request) =>
{
    try
    {
        await modbusMaster.WriteMultipleRegistersAsync(request.Ip, request.Port, request.UnitId, request.StartAddress, request.Values);
        return Results.Ok(new { success = true, message = "Registers written successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("WriteMultipleRegisters")
.WithSummary("Write multiple registers to a Modbus device");

// Add polling range
modbusApi.MapPost("/polling/add", (AddPollingRangeRequest request) =>
{
    try
    {
        var registerRange = new RegisterRange
        {
            RegisterType = request.RegisterType,
            UnitId = request.UnitId,
            StartAddress = request.StartAddress,
            Count = request.Count,
            PollingInterval = TimeSpan.FromMilliseconds(request.PollingIntervalMs)
        };

        modbusMaster.AddPollingRange(request.Ip, request.Port, registerRange);
        return Results.Ok(new { success = true, message = "Polling range added successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("AddPollingRange")
.WithSummary("Add a register range for automatic polling");

// Remove polling range
modbusApi.MapPost("/polling/remove", (RemovePollingRangeRequest request) =>
{
    try
    {
        var registerRange = new RegisterRange
        {
            RegisterType = request.RegisterType,
            UnitId = request.UnitId,
            StartAddress = request.StartAddress,
            Count = request.Count
        };

        modbusMaster.RemovePollingRange(request.Ip, request.Port, registerRange);
        return Results.Ok(new { success = true, message = "Polling range removed successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("RemovePollingRange")
.WithSummary("Remove a register range from automatic polling");

// Get connection status
modbusApi.MapGet("/connections", () =>
{
    try
    {
        var connections = modbusMaster.ConnectionManager.GetConnectionInfo();
        return Results.Ok(new { success = true, connections });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("GetConnections")
.WithSummary("Get status of all connections");

// Get polling ranges
modbusApi.MapGet("/polling", (string ip, int port) =>
{
    try
    {
        var ranges = modbusMaster.PollingEngine.GetPollingRanges(ip, port);
        return Results.Ok(new { success = true, ranges });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("GetPollingRanges")
.WithSummary("Get polling ranges for a device");

// Convert register values
modbusApi.MapPost("/convert", async (ConvertDataRequest request) =>
{
    try
    {
        // Read the registers first
        var registers = await modbusMaster.ReadHoldingRegistersAsync(
            request.Ip, request.Port, request.UnitId, request.StartAddress, request.Count);

        // Convert using the provided configurations
        var convertedValues = DataConverter.ConvertMultiple(registers, request.Conversions, request.StartAddress);

        return Results.Ok(new {
            success = true,
            rawValues = registers,
            convertedValues = convertedValues.Select(cv => new {
                name = cv.Config.Name,
                value = cv.Value,
                dataType = cv.Config.DataType.ToString(),
                endianType = cv.Config.EndianType.ToString(),
                quality = cv.Quality.ToString(),
                errorMessage = cv.ErrorMessage
            })
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ConvertData")
.WithSummary("Read and convert register data with specified configurations");

// Monitoring API endpoints
var monitoringApi = app.MapGroup("/monitoring").WithTags("Monitoring & Statistics");

// Get system monitoring info
monitoringApi.MapGet("/system", () =>
{
    try
    {
        var monitoringService = app.Services.GetRequiredService<MonitoringService>();
        var systemInfo = monitoringService.GetSystemMonitoringInfo();

        return Results.Ok(new { success = true, systemInfo });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("GetSystemMonitoring")
.WithSummary("Get overall system monitoring information");

// Get test device monitoring info
monitoringApi.MapGet("/test-device", () =>
{
    try
    {
        var monitoringService = app.Services.GetRequiredService<MonitoringService>();
        var endpointInfo = monitoringService.GetEndpointMonitoringInfo("***************", 502);

        return Results.Ok(new { success = true, endpointInfo });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("GetTestDeviceMonitoring")
.WithSummary("Get monitoring information for test device");

// Get performance metrics
monitoringApi.MapGet("/metrics", (int? minutes) =>
{
    try
    {
        var monitoringService = app.Services.GetRequiredService<MonitoringService>();
        var timeSpan = TimeSpan.FromMinutes(minutes ?? 10);
        var metrics = monitoringService.GetPerformanceMetrics(timeSpan);

        return Results.Ok(new { success = true, metrics, timeSpanMinutes = timeSpan.TotalMinutes });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("GetPerformanceMetrics")
.WithSummary("Get performance metrics for specified time period");

// Management API endpoints
var managementApi = app.MapGroup("/management").WithTags("Management & Control");

// Add polling range to test device
managementApi.MapPost("/add-polling", (AddTestPollingRequest request) =>
{
    try
    {
        var registerRange = new RegisterRange
        {
            RegisterType = request.RegisterType,
            UnitId = request.UnitId,
            StartAddress = request.StartAddress,
            Count = request.Count,
            PollingInterval = TimeSpan.FromMilliseconds(request.PollingIntervalMs)
        };

        modbusMaster.AddPollingRange("***************", 502, registerRange);
        return Results.Ok(new {
            success = true,
            message = $"Added polling range: {request.RegisterType} Unit:{request.UnitId} Address:{request.StartAddress} Count:{request.Count}",
            pollingIntervalMs = request.PollingIntervalMs
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("AddTestPolling")
.WithSummary("Add polling range to test device");

// Remove polling range from test device
managementApi.MapPost("/remove-polling", (RemoveTestPollingRequest request) =>
{
    try
    {
        var registerRange = new RegisterRange
        {
            RegisterType = request.RegisterType,
            UnitId = request.UnitId,
            StartAddress = request.StartAddress,
            Count = request.Count
        };

        modbusMaster.RemovePollingRange("***************", 502, registerRange);
        return Results.Ok(new {
            success = true,
            message = $"Removed polling range: {request.RegisterType} Unit:{request.UnitId} Address:{request.StartAddress} Count:{request.Count}"
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("RemoveTestPolling")
.WithSummary("Remove polling range from test device");

// Clear request queue
managementApi.MapPost("/clear-queue", () =>
{
    try
    {
        modbusMaster.RequestQueueManager?.ClearQueue("***************", 502);
        return Results.Ok(new { success = true, message = "Request queue cleared for test device" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, error = ex.Message });
    }
})
.WithName("ClearQueue")
.WithSummary("Clear request queue for test device");

// Example of how to use monitoring service directly
// This demonstrates how to access monitoring information programmatically
var appMonitoringService = app.Services.GetRequiredService<MonitoringService>();

// You can access monitoring information like this:
// var systemInfo = appMonitoringService.GetSystemMonitoringInfo();
// var endpointInfo = appMonitoringService.GetEndpointMonitoringInfo("***************", 502);
// var metrics = appMonitoringService.GetPerformanceMetrics(TimeSpan.FromMinutes(10));

// Add a root endpoint that redirects to Swagger UI
app.MapGet("/", () => Results.Redirect("/swagger"))
    .WithName("Root")
    .WithSummary("Redirect to Swagger UI")
    .ExcludeFromDescription();

// Add API info endpoint
app.MapGet("/api/info", () => Results.Ok(new {
    title = "Modbus TCP Master API",
    version = "v1.0.0",
    description = "Enterprise-grade Modbus TCP Master API for industrial automation",
    features = new[] {
        "ModbusTCP protocol support",
        "Real-time polling with configurable intervals",
        "Dynamic write insertion during polling",
        "Multiple connections per endpoint",
        "Instance-based connection management",
        "Enterprise monitoring and statistics",
        "High-performance parallel processing"
    },
    testDevice = new {
        endpoint = "***************:502",
        preConfigured = true,
        description = "Pre-configured test device with polling ranges and dynamic write support"
    },
    documentation = "/swagger",
    healthCheck = "/health"
}))
.WithTags("API Information")
.WithName("GetApiInfo")
.WithSummary("Get API information and capabilities");

app.Run();

// Request models for general Modbus API
record WriteCoilRequest(string Ip, int Port, byte UnitId, ushort Address, bool Value);
record WriteRegisterRequest(string Ip, int Port, byte UnitId, ushort Address, ushort Value);
record WriteMultipleCoilsRequest(string Ip, int Port, byte UnitId, ushort StartAddress, bool[] Values);
record WriteMultipleRegistersRequest(string Ip, int Port, byte UnitId, ushort StartAddress, ushort[] Values);
record AddPollingRangeRequest(string Ip, int Port, byte UnitId, ModbusRegisterType RegisterType, ushort StartAddress, ushort Count, int PollingIntervalMs);
record RemovePollingRangeRequest(string Ip, int Port, byte UnitId, ModbusRegisterType RegisterType, ushort StartAddress, ushort Count);
record ConvertDataRequest(string Ip, int Port, byte UnitId, ushort StartAddress, ushort Count, DataConversionConfig[] Conversions);

// Request models for test device API (***************:502)
record DynamicWriteRequest(byte UnitId, ushort Address, ushort Value, bool IsCoil = false);
record BatchWriteRequest(DynamicWriteRequest[] Writes);
record AddTestPollingRequest(byte UnitId, ModbusRegisterType RegisterType, ushort StartAddress, ushort Count, int PollingIntervalMs);
record RemoveTestPollingRequest(byte UnitId, ModbusRegisterType RegisterType, ushort StartAddress, ushort Count);
