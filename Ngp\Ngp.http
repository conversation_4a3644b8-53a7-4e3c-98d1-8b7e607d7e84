@Ngp_HostAddress = http://localhost:5000

### Test Device API (***************:502) ###

# 1. Check test device status and connections
GET {{Ngp_HostAddress}}/test-device/status
Accept: application/json

###

# 2. Quick read test from pre-configured addresses
GET {{Ng<PERSON>_HostAddress}}/test-device/quick-read
Accept: application/json

###

# 3. Get polling status and configuration
GET {{Ngp_HostAddress}}/test-device/polling-status
Accept: application/json

###

# 4. Dynamic write to register (will be inserted into polling)
POST {{Ngp_HostAddress}}/test-device/dynamic-write
Content-Type: application/json

{
  "unitId": 1,
  "address": 10,
  "value": 1234,
  "isCoil": false
}

###

# 5. Dynamic write to coil (will be inserted into polling)
POST {{Ngp_HostAddress}}/test-device/dynamic-write
Content-Type: application/json

{
  "unitId": 1,
  "address": 5,
  "value": 1,
  "isCoil": true
}

###

# 6. <PERSON>ch write multiple values
POST {{Ngp_HostAddress}}/test-device/batch-write
Content-Type: application/json

{
  "writes": [
    {
      "unitId": 1,
      "address": 20,
      "value": 2000,
      "isCoil": false
    },
    {
      "unitId": 1,
      "address": 21,
      "value": 2001,
      "isCoil": false
    },
    {
      "unitId": 1,
      "address": 10,
      "value": 1,
      "isCoil": true
    }
  ]
}

###

### Monitoring API ###

# 7. Get system monitoring information
GET {{Ngp_HostAddress}}/monitoring/system
Accept: application/json

###

# 8. Get test device specific monitoring
GET {{Ngp_HostAddress}}/monitoring/test-device
Accept: application/json

###

# 9. Get performance metrics (last 5 minutes)
GET {{Ngp_HostAddress}}/monitoring/metrics?minutes=5
Accept: application/json

###

### Management API ###

# 10. Add new polling range to test device
POST {{Ngp_HostAddress}}/management/add-polling
Content-Type: application/json

{
  "unitId": 1,
  "registerType": "HoldingRegister",
  "startAddress": 200,
  "count": 10,
  "pollingIntervalMs": 3000
}

###

# 11. Remove polling range from test device
POST {{Ngp_HostAddress}}/management/remove-polling
Content-Type: application/json

{
  "unitId": 1,
  "registerType": "HoldingRegister",
  "startAddress": 200,
  "count": 10
}

###

# 12. Clear request queue for test device
POST {{Ngp_HostAddress}}/management/clear-queue
Content-Type: application/json

###

### General Modbus API (for any device) ###

# 13. Read holding registers from test device
GET {{Ngp_HostAddress}}/modbus/holding-registers?ip=***************&port=502&unitId=1&startAddress=0&quantity=10
Accept: application/json

###

# 14. Read coils from test device
GET {{Ngp_HostAddress}}/modbus/coils?ip=***************&port=502&unitId=1&startAddress=0&quantity=8
Accept: application/json

###

# 15. Write single register to test device
POST {{Ngp_HostAddress}}/modbus/register
Content-Type: application/json

{
  "ip": "***************",
  "port": 502,
  "unitId": 1,
  "address": 15,
  "value": 5555
}

###

# 16. Write single coil to test device
POST {{Ngp_HostAddress}}/modbus/coil
Content-Type: application/json

{
  "ip": "***************",
  "port": 502,
  "unitId": 1,
  "address": 15,
  "value": true
}

###

# 17. Get all connections status
GET {{Ngp_HostAddress}}/modbus/connections
Accept: application/json

###

# 18. Get polling ranges for test device
GET {{Ngp_HostAddress}}/modbus/polling?ip=***************&port=502
Accept: application/json

###
